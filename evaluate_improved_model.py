#!/usr/bin/env python3
"""
Quick evaluation of the improved model
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.metrics import classification_report, confusion_matrix, f1_score
import json
from datetime import datetime

# Configuration
IMAGE_SIZE = 150
BATCH_SIZE = 32
MODEL_PATH = 'models/malaria_improved_efficientnet_20250630_215718.keras'

def evaluate_improved_model():
    """Evaluate the improved model"""
    print("🔬 EVALUATING IMPROVED MALARIA DETECTION MODEL")
    print("=" * 50)
    
    # Load model
    print(f"Loading model: {MODEL_PATH}")
    model = tf.keras.models.load_model(MODEL_PATH)
    
    # Prepare test data
    test_datagen = ImageDataGenerator(rescale=1./255)
    
    test_generator = test_datagen.flow_from_directory(
        'data/test',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        shuffle=False
    )
    
    print(f"Test samples: {test_generator.samples}")
    
    # Evaluate model
    print("Evaluating model...")
    test_loss, test_accuracy, test_precision, test_recall = model.evaluate(test_generator, verbose=1)
    
    # Generate predictions
    print("Generating predictions...")
    test_generator.reset()
    predictions = model.predict(test_generator, verbose=1)
    predicted_classes = (predictions > 0.5).astype(int).flatten()
    
    # Get true labels
    true_classes = test_generator.classes
    
    # Calculate F1 score
    f1 = f1_score(true_classes, predicted_classes)
    
    # Classification report
    class_names = ['Parasitized', 'Uninfected']
    report = classification_report(
        true_classes, 
        predicted_classes, 
        target_names=class_names,
        output_dict=True
    )
    
    # Confusion matrix
    cm = confusion_matrix(true_classes, predicted_classes)
    
    # Print results
    print("\n" + "=" * 50)
    print("🎉 IMPROVED MODEL EVALUATION RESULTS")
    print("=" * 50)
    print(f"Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
    print(f"Test Precision: {test_precision:.4f} ({test_precision*100:.2f}%)")
    print(f"Test Recall: {test_recall:.4f} ({test_recall*100:.2f}%)")
    print(f"Test F1-Score: {f1:.4f} ({f1*100:.2f}%)")
    print(f"Test Loss: {test_loss:.4f}")
    
    print("\n📊 Detailed Classification Report:")
    print(classification_report(true_classes, predicted_classes, target_names=class_names))
    
    print("\n📈 Confusion Matrix:")
    print(f"True Positives (Parasitized correctly identified): {cm[0][0]}")
    print(f"False Negatives (Parasitized missed): {cm[0][1]}")
    print(f"False Positives (Uninfected misclassified): {cm[1][0]}")
    print(f"True Negatives (Uninfected correctly identified): {cm[1][1]}")
    
    # Calculate additional metrics
    sensitivity = cm[0][0] / (cm[0][0] + cm[0][1]) if (cm[0][0] + cm[0][1]) > 0 else 0
    specificity = cm[1][1] / (cm[1][1] + cm[1][0]) if (cm[1][1] + cm[1][0]) > 0 else 0
    
    print(f"\n🔍 Additional Metrics:")
    print(f"Sensitivity (True Positive Rate): {sensitivity:.4f} ({sensitivity*100:.2f}%)")
    print(f"Specificity (True Negative Rate): {specificity:.4f} ({specificity*100:.2f}%)")
    
    # Save results
    results = {
        'model_name': 'improved_efficientnet',
        'test_accuracy': float(test_accuracy),
        'test_precision': float(test_precision),
        'test_recall': float(test_recall),
        'test_f1_score': float(f1),
        'test_loss': float(test_loss),
        'sensitivity': float(sensitivity),
        'specificity': float(specificity),
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'total_test_samples': len(true_classes),
        'timestamp': datetime.now().isoformat()
    }
    
    # Create results directory
    os.makedirs('training_results', exist_ok=True)
    
    # Save to file
    results_file = f'training_results/improved_model_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    # Compare with original model
    print("\n🔄 COMPARISON WITH ORIGINAL MODEL:")
    print("=" * 50)
    print("Original Model:")
    print("  - Accuracy: 49.77%")
    print("  - Precision: 0% (all predicted as Uninfected)")
    print("  - Recall: 0% (no Parasitized detected)")
    print("  - F1-Score: 0%")
    print()
    print("Improved Model:")
    print(f"  - Accuracy: {test_accuracy*100:.2f}% (Improvement: {(test_accuracy-0.4977)*100:+.2f}%)")
    print(f"  - Precision: {test_precision*100:.2f}% (Improvement: {test_precision*100:+.2f}%)")
    print(f"  - Recall: {test_recall*100:.2f}% (Improvement: {test_recall*100:+.2f}%)")
    print(f"  - F1-Score: {f1*100:.2f}% (Improvement: {f1*100:+.2f}%)")
    
    return results

if __name__ == "__main__":
    evaluate_improved_model()
