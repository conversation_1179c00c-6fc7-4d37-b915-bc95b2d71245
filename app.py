"""
Malaria Detection Web Application
Flask web interface for malaria cell image classification
"""

import os
import uuid
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
from PIL import Image
import logging
from datetime import datetime

# Import our prediction service
from src.prediction_service import prediction_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'malaria_detection_secret_key_2024'

# Configuration
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE

# Create upload directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_image(file_path):
    """Validate that the uploaded file is a valid image"""
    try:
        with Image.open(file_path) as img:
            img.verify()
        return True
    except Exception:
        return False

@app.route('/')
def index():
    """Main page"""
    model_info = prediction_service.get_model_info()
    return render_template('index.html', model_info=model_info)

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and prediction"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            flash('Keine Datei ausgewählt', 'error')
            return redirect(url_for('index'))
        
        file = request.files['file']
        
        # Check if file was selected
        if file.filename == '':
            flash('Keine Datei ausgewählt', 'error')
            return redirect(url_for('index'))
        
        # Check file type
        if not allowed_file(file.filename):
            flash('Ungültiger Dateityp. Erlaubte Formate: PNG, JPG, JPEG, GIF, BMP, TIFF', 'error')
            return redirect(url_for('index'))
        
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        
        # Save file
        file.save(file_path)
        
        # Validate image
        if not validate_image(file_path):
            os.remove(file_path)
            flash('Ungültige Bilddatei', 'error')
            return redirect(url_for('index'))
        
        # Make prediction
        result = prediction_service.predict(file_path)
        
        # Prepare result data
        result_data = {
            'filename': unique_filename,
            'original_filename': filename,
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'prediction': result.get('prediction'),
            'confidence': result.get('confidence'),
            'probability': result.get('probability'),
            'error': result.get('error')
        }
        
        return render_template('result.html', result=result_data)
        
    except Exception as e:
        logger.error(f"Error in upload_file: {str(e)}")
        flash(f'Fehler beim Verarbeiten der Datei: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint for predictions"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Keine Datei bereitgestellt'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'error': 'Keine Datei ausgewählt'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Ungültiger Dateityp'}), 400
        
        # Save temporary file
        filename = secure_filename(file.filename)
        unique_filename = f"temp_{uuid.uuid4()}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        try:
            # Validate image
            if not validate_image(file_path):
                return jsonify({'error': 'Ungültige Bilddatei'}), 400
            
            # Make prediction
            result = prediction_service.predict(file_path)
            
            # Clean up temporary file
            os.remove(file_path)
            
            if result.get('error'):
                return jsonify({'error': result['error']}), 500
            
            return jsonify({
                'prediction': result['prediction'],
                'confidence': result['confidence'],
                'probability': result['probability'],
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            # Clean up temporary file on error
            if os.path.exists(file_path):
                os.remove(file_path)
            raise e
            
    except Exception as e:
        logger.error(f"Error in api_predict: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/model-info')
def model_info():
    """Get model information"""
    info = prediction_service.get_model_info()
    return jsonify(info)

@app.route('/analysis')
def analysis():
    """Show algorithm analysis page with metrics"""
    # Real metrics data from our trained model
    metrics_data = {
        'accuracy': 0.4977,  # 49.77% - Real accuracy from training
        'precision': {
            'parasitized': 0.00,  # 0% - Model didn't predict any parasitized correctly
            'uninfected': 0.50,   # 50% - Model predicted all as uninfected
            'average': 0.25       # 25% - Weighted average
        },
        'recall': {
            'parasitized': 0.00,  # 0% - No parasitized cases detected
            'uninfected': 1.00,   # 100% - All uninfected cases detected
            'average': 0.50       # 50% - Weighted average
        },
        'f1_score': {
            'parasitized': 0.00,  # 0% - No F1 score for parasitized
            'uninfected': 0.66,   # 66% - F1 score for uninfected
            'average': 0.33       # 33% - Weighted average
        },
        'confusion_matrix': {
            'true_positive': 0,     # No parasitized correctly identified
            'true_negative': 7880,  # All uninfected correctly identified
            'false_positive': 0,    # No false positives
            'false_negative': 7952  # All parasitized missed
        },
        'total_samples': 15832,  # Real total from training
        'training_time': '8 Minuten 15 Sekunden',
        'model_size': '12.6 MB',
        'last_updated': '2025-06-30 21:44:00',
        'training_note': 'Das Modell benötigt weitere Optimierung - es klassifiziert alle Bilder als "Uninfected"'
    }

    # Calculate additional metrics
    cm = metrics_data['confusion_matrix']
    if (cm['true_negative'] + cm['false_positive']) > 0:
        metrics_data['specificity'] = cm['true_negative'] / (cm['true_negative'] + cm['false_positive'])
    else:
        metrics_data['specificity'] = 0.0

    if (cm['true_positive'] + cm['false_negative']) > 0:
        metrics_data['sensitivity'] = cm['true_positive'] / (cm['true_positive'] + cm['false_negative'])
    else:
        metrics_data['sensitivity'] = 0.0

    return render_template('analysis.html', metrics=metrics_data)

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'model_loaded': prediction_service.model is not None
    })

@app.errorhandler(413)
def too_large(e):
    """Handle file too large error"""
    flash('Datei zu groß. Maximale Größe: 16MB', 'error')
    return redirect(url_for('index'))

@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(e):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(e)}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    # Check for trained models
    model_dir = 'models'
    if os.path.exists(model_dir):
        model_files = [f for f in os.listdir(model_dir) if f.endswith('.keras') or f.endswith('.h5')]
        if model_files:
            # Prefer .keras files over .h5
            keras_files = [f for f in model_files if f.endswith('.keras')]
            if keras_files:
                model_path = os.path.join(model_dir, keras_files[0])
            else:
                model_path = os.path.join(model_dir, model_files[0])
            prediction_service.load_model(model_path)
            logger.info(f"Loaded model: {model_path}")
        else:
            logger.warning("No trained models found. Please train a model first.")
    
    # Run the app
    app.run(debug=True, host='0.0.0.0', port=5000)