{"model_name": "improved_efficientnet", "test_accuracy": 0.49772611260414124, "test_precision": 0.49772611260414124, "test_recall": 1.0, "test_f1_score": 0.664642375168691, "test_loss": 0.7871750593185425, "sensitivity": 0.0, "specificity": 1.0, "classification_report": {"Parasitized": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 7952.0}, "Uninfected": {"precision": 0.49772612430520463, "recall": 1.0, "f1-score": 0.664642375168691, "support": 7880.0}, "accuracy": 0.49772612430520463, "macro avg": {"precision": 0.24886306215260232, "recall": 0.5, "f1-score": 0.3323211875843455, "support": 15832.0}, "weighted avg": {"precision": 0.24773129481588002, "recall": 0.49772612430520463, "f1-score": 0.33080987344171836, "support": 15832.0}}, "confusion_matrix": [[0, 7952], [0, 7880]], "total_test_samples": 15832, "timestamp": "2025-06-30T23:59:00.886837"}