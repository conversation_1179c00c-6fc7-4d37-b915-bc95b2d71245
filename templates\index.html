{% extends "base.html" %}

{% block title %}Malaria Erkennung - Upload{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Navigation -->
    <div class="text-center mb-4">
        <ul class="nav nav-pills justify-content-center">
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Startseite
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('analysis') }}">
                    <i class="fas fa-chart-bar"></i> Algorithmus Analyse
                </a>
            </li>
        </ul>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- Model Status -->
        <div class="model-info">
            <h5><i class="fas fa-info-circle"></i> Modell Status</h5>
            {% if model_info.status == 'Model loaded' %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> Modell erfolgreich geladen
                    <small class="d-block mt-1">
                        Parameter: {{ "{:,}".format(model_info.total_params) if model_info.total_params else 'N/A' }}
                    </small>
                </div>
            {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> {{ model_info.status }}
                    <small class="d-block mt-1">
                        Bitte trainieren Sie zuerst ein Modell mit dem Malaria.py Skript.
                    </small>
                </div>
            {% endif %}
        </div>

        <!-- Upload Form -->
        <form method="POST" action="{{ url_for('upload_file') }}" enctype="multipart/form-data" id="upload-form">
            <div class="upload-area" id="upload-area">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h4>Blutzellen-Bild hochladen</h4>
                <p class="text-muted mb-3">
                    Ziehen Sie ein Bild hierher oder klicken Sie zum Auswählen
                </p>
                <p class="small text-muted">
                    Unterstützte Formate: PNG, JPG, JPEG, GIF, BMP, TIFF<br>
                    Maximale Dateigröße: 16MB
                </p>
                
                <input type="file" 
                       name="file" 
                       id="file-input" 
                       accept="image/*" 
                       style="display: none;" 
                       required>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-custom" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open"></i> Datei auswählen
                    </button>
                </div>
            </div>
            
            <!-- File Preview -->
            <div id="file-preview" style="display: none;">
                <div class="card mt-4">
                    <div class="card-body text-center">
                        <h6>Ausgewählte Datei:</h6>
                        <p id="file-name" class="text-muted"></p>
                        <div id="image-preview" class="mt-3"></div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-custom btn-lg">
                                <i class="fas fa-search"></i> Analyse starten
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetForm()">
                                <i class="fas fa-times"></i> Zurücksetzen
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Loading Animation -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <h5>Analysiere Bild...</h5>
            <p class="text-muted">Dies kann einen Moment dauern</p>
        </div>

        <!-- Information Section -->
        <div class="row mt-5">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-microscope text-primary"></i> Über die Analyse
                        </h5>
                        <p class="card-text">
                            Dieses System verwendet Deep Learning zur Erkennung von Malaria-Parasiten 
                            in Blutzellen-Bildern. Das neuronale Netzwerk wurde mit Tausenden von 
                            Bildern trainiert, um zwischen infizierten und nicht-infizierten Zellen 
                            zu unterscheiden.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-shield-alt text-success"></i> Wichtiger Hinweis
                        </h5>
                        <p class="card-text">
                            Diese Anwendung dient nur zu Demonstrationszwecken und ersetzt 
                            keine professionelle medizinische Diagnose. Bei Verdacht auf Malaria 
                            konsultieren Sie bitte einen Arzt oder ein medizinisches Labor.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Instructions -->
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-question-circle text-info"></i> Anleitung
                </h5>
                <ol class="mb-0">
                    <li>Laden Sie ein Bild einer Blutzelle hoch (idealerweise ein mikroskopisches Bild)</li>
                    <li>Klicken Sie auf "Analyse starten"</li>
                    <li>Das System analysiert das Bild und gibt eine Vorhersage aus</li>
                    <li>Sie erhalten das Ergebnis mit einer Konfidenz-Bewertung</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced file handling
    document.getElementById('file-input').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            showFilePreview(file);
        }
    });

    function showFilePreview(file) {
        const preview = document.getElementById('file-preview');
        const fileName = document.getElementById('file-name');
        const imagePreview = document.getElementById('image-preview');
        
        fileName.textContent = file.name;
        
        // Show image preview
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.innerHTML = `<img src="${e.target.result}" class="preview-image" alt="Preview">`;
        };
        reader.readAsDataURL(file);
        
        preview.style.display = 'block';
    }

    function resetForm() {
        document.getElementById('upload-form').reset();
        document.getElementById('file-preview').style.display = 'none';
        document.getElementById('image-preview').innerHTML = '';
    }

    // Show loading animation on form submit
    document.getElementById('upload-form').addEventListener('submit', function() {
        document.getElementById('loading').style.display = 'block';
        document.querySelector('.main-container').style.opacity = '0.7';
    });

    // Enhanced drag and drop
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');

    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // Drag and drop events
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadArea.classList.add('dragover');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            showFilePreview(files[0]);
        }
    }
</script>
{% endblock %}