"""
Simple Malaria Detection Model Training Script
Quick training script for demonstration purposes
"""

import os
import numpy as np
import logging
from datetime import datetime
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, BatchNormalization
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
IMAGE_SIZE = 128
BATCH_SIZE = 32
EPOCHS = 10  # Reduced for quick training
LEARNING_RATE = 0.001

# Paths
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATASET_DIR = os.path.join(BASE_DIR, 'data')
MODEL_DIR = os.path.join(BASE_DIR, 'models')
LOG_DIR = os.path.join(BASE_DIR, 'logs')

# Create directories
for directory in [MODEL_DIR, LOG_DIR]:
    os.makedirs(directory, exist_ok=True)

def create_simple_model():
    """Create a simple CNN model for quick training"""
    model = Sequential([
        Conv2D(32, (3, 3), activation='relu', input_shape=(IMAGE_SIZE, IMAGE_SIZE, 3)),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        
        Conv2D(64, (3, 3), activation='relu'),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        
        Conv2D(128, (3, 3), activation='relu'),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        
        Flatten(),
        Dense(128, activation='relu'),
        BatchNormalization(),
        Dropout(0.5),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE),
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def prepare_data():
    """Prepare training and validation data"""
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=20,
        width_shift_range=0.1,
        height_shift_range=0.1,
        shear_range=0.1,
        zoom_range=0.1,
        horizontal_flip=True,
        validation_split=0.2
    )
    
    test_datagen = ImageDataGenerator(rescale=1./255)
    
    try:
        logger.info("Loading training data...")
        training_set = train_datagen.flow_from_directory(
            os.path.join(DATASET_DIR, 'train'),
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            subset='training'
        )
        
        validation_set = train_datagen.flow_from_directory(
            os.path.join(DATASET_DIR, 'train'),
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            subset='validation'
        )
        
        # Check if test directory exists
        test_dir = os.path.join(DATASET_DIR, 'test')
        if os.path.exists(test_dir):
            test_set = test_datagen.flow_from_directory(
                test_dir,
                target_size=(IMAGE_SIZE, IMAGE_SIZE),
                batch_size=BATCH_SIZE,
                class_mode='binary',
                shuffle=False  # Important for evaluation
            )
        else:
            logger.warning("Test directory not found. Using validation set for testing.")
            test_set = validation_set
        
        logger.info(f"Training samples: {training_set.samples}")
        logger.info(f"Validation samples: {validation_set.samples}")
        logger.info(f"Test samples: {test_set.samples}")
        logger.info(f"Class indices: {training_set.class_indices}")
        
        return training_set, validation_set, test_set
        
    except Exception as e:
        logger.error(f"Error loading data: {str(e)}")
        raise

def train_model():
    """Train the model"""
    try:
        # Check if data directories exist
        train_dir = os.path.join(DATASET_DIR, 'train')
        if not os.path.exists(train_dir):
            raise FileNotFoundError(f"Training directory not found: {train_dir}")
        
        # Create model
        logger.info("Creating model...")
        model = create_simple_model()
        model.summary()
        
        # Prepare data
        training_set, validation_set, test_set = prepare_data()
        
        # Setup callbacks
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_path = os.path.join(MODEL_DIR, f'malaria_simple_model_{timestamp}.keras')
        
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=3,
                restore_best_weights=True,
                verbose=1
            ),
            ModelCheckpoint(
                model_path,
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # Train model
        logger.info("Starting training...")
        history = model.fit(
            training_set,
            steps_per_epoch=min(training_set.samples // BATCH_SIZE, 100),  # Limit steps for quick training
            epochs=EPOCHS,
            validation_data=validation_set,
            validation_steps=min(validation_set.samples // BATCH_SIZE, 50),
            callbacks=callbacks,
            verbose=1
        )
        
        # Plot training history
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        plt.plot(history.history['loss'], label='Training Loss')
        plt.plot(history.history['val_loss'], label='Validation Loss')
        plt.title('Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        plt.subplot(1, 2, 2)
        plt.plot(history.history['accuracy'], label='Training Accuracy')
        plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
        plt.title('Model Accuracy')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()
        
        plt.tight_layout()
        plot_path = os.path.join(LOG_DIR, f'training_history_{timestamp}.png')
        plt.savefig(plot_path)
        plt.close()
        
        logger.info(f"Training completed!")
        logger.info(f"Model saved: {model_path}")
        logger.info(f"Training plot saved: {plot_path}")
        
        # Evaluate on test set
        logger.info("Evaluating model...")
        test_loss, test_accuracy = model.evaluate(test_set, verbose=0)
        logger.info(f"Test Accuracy: {test_accuracy:.4f}")
        logger.info(f"Test Loss: {test_loss:.4f}")

        # Detailed evaluation with classification report and confusion matrix
        logger.info("Generating detailed classification report and confusion matrix...")
        test_set.reset()  # Reset generator before prediction
        y_pred_prob = model.predict(test_set, verbose=1)
        y_pred = (y_pred_prob > 0.5).astype("int32").flatten()
        y_true = test_set.classes
        class_labels = list(test_set.class_indices.keys())

        # Classification Report (Precision, Recall, F1-Score)
        report = classification_report(y_true, y_pred, target_names=class_labels)
        logger.info("Classification Report:\n" + report)

        # Confusion Matrix
        cm = confusion_matrix(y_true, y_pred)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=class_labels, yticklabels=class_labels)
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        cm_path = os.path.join(LOG_DIR, f'confusion_matrix_{timestamp}.png')
        plt.savefig(cm_path)
        plt.close()
        logger.info(f"Confusion matrix saved: {cm_path}")

        return model_path, history
    except Exception as e:
        logger.error(f"Error during training: {str(e)}")
        raise

def main():
    """Main function"""
    try:
        logger.info("Starting simple malaria detection model training...")
        logger.info(f"Configuration:")
        logger.info(f"  Image Size: {IMAGE_SIZE}x{IMAGE_SIZE}")
        logger.info(f"  Batch Size: {BATCH_SIZE}")
        logger.info(f"  Epochs: {EPOCHS}")
        logger.info(f"  Learning Rate: {LEARNING_RATE}")
        
        model_path, history = train_model()
        
        logger.info("Training completed successfully!")
        logger.info(f"You can now run the web application with: python app.py")
        
        return model_path
        
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()