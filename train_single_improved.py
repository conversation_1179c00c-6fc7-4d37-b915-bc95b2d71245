#!/usr/bin/env python3
"""
Single Improved Malaria Detection Model Training
Focused training with the best architecture for quick results
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.applications import EfficientNetB0
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Dropout, BatchNormalization
from tensorflow.keras.models import Sequential
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import Model<PERSON>heckpoint, EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.metrics import classification_report, confusion_matrix, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import logging

# Configuration
IMAGE_SIZE = 150
BATCH_SIZE = 32
EPOCHS = 30  # Reduced for faster training
LEARNING_RATE = 0.0001
MODEL_DIR = 'models'
LOG_DIR = 'logs'
RESULTS_DIR = 'training_results'

# Create directories
for directory in [MODEL_DIR, LOG_DIR, RESULTS_DIR]:
    os.makedirs(directory, exist_ok=True)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_improved_model():
    """Create improved EfficientNet-based model"""
    logger.info("Creating improved EfficientNet model...")
    
    # Load pre-trained EfficientNet
    base_model = EfficientNetB0(
        weights='imagenet',
        include_top=False,
        input_shape=(IMAGE_SIZE, IMAGE_SIZE, 3)
    )
    
    # Freeze base model initially
    base_model.trainable = False
    
    # Build model
    model = Sequential([
        base_model,
        GlobalAveragePooling2D(),
        BatchNormalization(),
        Dropout(0.5),
        Dense(256, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        Dense(128, activation='relu'),
        Dropout(0.2),
        Dense(1, activation='sigmoid')
    ])
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE),
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )

    # Build model to count parameters
    model.build((None, IMAGE_SIZE, IMAGE_SIZE, 3))
    logger.info(f"Model created with {model.count_params():,} parameters")
    return model, base_model

def prepare_improved_data():
    """Prepare data with improved augmentation"""
    logger.info("Preparing data with improved augmentation...")
    
    # Enhanced training data generator
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=30,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=True,
        brightness_range=[0.8, 1.2],
        validation_split=0.2
    )
    
    # Test data generator
    test_datagen = ImageDataGenerator(rescale=1./255)
    
    # Create generators
    train_generator = train_datagen.flow_from_directory(
        'data/train',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        subset='training',
        shuffle=True,
        seed=42
    )
    
    validation_generator = train_datagen.flow_from_directory(
        'data/train',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        subset='validation',
        shuffle=True,
        seed=42
    )
    
    test_generator = test_datagen.flow_from_directory(
        'data/test',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        shuffle=False
    )
    
    logger.info(f"Training samples: {train_generator.samples}")
    logger.info(f"Validation samples: {validation_generator.samples}")
    logger.info(f"Test samples: {test_generator.samples}")
    
    return train_generator, validation_generator, test_generator

def train_improved_model():
    """Train the improved model"""
    logger.info("🚀 Starting improved model training...")
    
    # Prepare data
    train_gen, val_gen, test_gen = prepare_improved_data()
    
    # Create model
    model, base_model = create_improved_model()
    
    # Setup callbacks
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(MODEL_DIR, f'malaria_improved_efficientnet_{timestamp}.keras')
    
    callbacks = [
        ModelCheckpoint(
            model_path,
            monitor='val_accuracy',
            save_best_only=True,
            save_weights_only=False,
            mode='max',
            verbose=1
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=8,
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=4,
            min_lr=1e-7,
            verbose=1
        )
    ]
    
    # Phase 1: Train with frozen base
    logger.info("Phase 1: Training with frozen base model...")
    history1 = model.fit(
        train_gen,
        epochs=EPOCHS // 2,
        validation_data=val_gen,
        callbacks=callbacks,
        verbose=1
    )
    
    # Phase 2: Fine-tuning
    logger.info("Phase 2: Fine-tuning with unfrozen base model...")
    base_model.trainable = True
    
    # Lower learning rate for fine-tuning
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE / 10),
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )
    
    history2 = model.fit(
        train_gen,
        epochs=EPOCHS // 2,
        validation_data=val_gen,
        callbacks=callbacks,
        verbose=1,
        initial_epoch=len(history1.history['loss'])
    )
    
    # Combine histories
    for key in history1.history:
        history1.history[key].extend(history2.history[key])
    
    logger.info(f"Training completed! Model saved to: {model_path}")
    
    # Evaluate model
    evaluate_model(model_path, test_gen, history1)
    
    return model_path

def evaluate_model(model_path, test_gen, history):
    """Evaluate the trained model"""
    logger.info("Evaluating model performance...")
    
    # Load best model
    model = tf.keras.models.load_model(model_path)
    
    # Evaluate on test set
    test_loss, test_accuracy, test_precision, test_recall = model.evaluate(test_gen, verbose=1)
    
    # Generate predictions
    test_gen.reset()
    predictions = model.predict(test_gen, verbose=1)
    predicted_classes = (predictions > 0.5).astype(int).flatten()
    
    # Get true labels
    true_classes = test_gen.classes
    
    # Calculate F1 score
    f1 = f1_score(true_classes, predicted_classes)
    
    # Classification report
    class_names = ['Parasitized', 'Uninfected']
    report = classification_report(
        true_classes, 
        predicted_classes, 
        target_names=class_names,
        output_dict=True
    )
    
    # Confusion matrix
    cm = confusion_matrix(true_classes, predicted_classes)
    
    # Print results
    logger.info("=" * 50)
    logger.info("IMPROVED MODEL EVALUATION RESULTS")
    logger.info("=" * 50)
    logger.info(f"Test Accuracy: {test_accuracy:.4f}")
    logger.info(f"Test Precision: {test_precision:.4f}")
    logger.info(f"Test Recall: {test_recall:.4f}")
    logger.info(f"Test F1-Score: {f1:.4f}")
    logger.info(f"Test Loss: {test_loss:.4f}")
    logger.info("\nClassification Report:")
    print(classification_report(true_classes, predicted_classes, target_names=class_names))
    
    # Save results
    results = {
        'test_accuracy': float(test_accuracy),
        'test_precision': float(test_precision),
        'test_recall': float(test_recall),
        'test_f1_score': float(f1),
        'test_loss': float(test_loss),
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'total_test_samples': len(true_classes),
        'timestamp': datetime.now().isoformat()
    }
    
    # Save to file
    results_file = os.path.join(RESULTS_DIR, f'improved_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Plot confusion matrix
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
               xticklabels=class_names, yticklabels=class_names)
    plt.title('Improved Model - Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'improved_confusion_matrix.png'), dpi=300)
    plt.close()
    
    # Plot training history
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Accuracy
    axes[0, 0].plot(history.history['accuracy'], label='Training')
    axes[0, 0].plot(history.history['val_accuracy'], label='Validation')
    axes[0, 0].set_title('Model Accuracy')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].legend()
    
    # Loss
    axes[0, 1].plot(history.history['loss'], label='Training')
    axes[0, 1].plot(history.history['val_loss'], label='Validation')
    axes[0, 1].set_title('Model Loss')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    
    # Precision
    axes[1, 0].plot(history.history['precision'], label='Training')
    axes[1, 0].plot(history.history['val_precision'], label='Validation')
    axes[1, 0].set_title('Model Precision')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Precision')
    axes[1, 0].legend()
    
    # Recall
    axes[1, 1].plot(history.history['recall'], label='Training')
    axes[1, 1].plot(history.history['val_recall'], label='Validation')
    axes[1, 1].set_title('Model Recall')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Recall')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'improved_training_history.png'), dpi=300)
    plt.close()
    
    logger.info("📊 Evaluation complete! Check 'training_results/' for visualizations")
    
    return results

def main():
    """Main function"""
    logger.info("🔬 IMPROVED MALARIA DETECTION MODEL TRAINING")
    logger.info("=" * 50)
    
    try:
        model_path = train_improved_model()
        logger.info("✅ Training completed successfully!")
        logger.info(f"📁 Model saved to: {model_path}")
        logger.info("📊 Check 'training_results/' directory for detailed results")
        
    except Exception as e:
        logger.error(f"❌ Training failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
