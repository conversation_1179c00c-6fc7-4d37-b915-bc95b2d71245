#!/usr/bin/env python3
"""
Improved Malaria Detection Model Training
Implements advanced techniques for better performance:
- Transfer Learning with pre-trained models
- Advanced data augmentation
- Optimized hyperparameters
- Better training strategies
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.applications import EfficientNetB0, ResNet50V2, MobileNetV2
from tensorflow.keras.layers import (
    Dense, GlobalAveragePooling2D, Dropout, BatchNormalization,
    Conv2D, MaxPooling2D, Flatten, Input
)
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.optimizers import Adam, SGD
from tensorflow.keras.callbacks import (
    ModelCheckpoint, EarlyStopping, ReduceLROnPlateau, 
    LearningRateScheduler, TensorBoard
)
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.utils import plot_model
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import logging

# Configuration
IMAGE_SIZE = 150  # Increased from 128 for better feature extraction
BATCH_SIZE = 32   # Optimized batch size
EPOCHS = 50       # More epochs with early stopping
LEARNING_RATE = 0.0001  # Lower learning rate for fine-tuning
MODEL_DIR = 'models'
LOG_DIR = 'logs'
RESULTS_DIR = 'training_results'

# Create directories
for directory in [MODEL_DIR, LOG_DIR, RESULTS_DIR]:
    os.makedirs(directory, exist_ok=True)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, 'improved_training.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedDataAugmentation:
    """Advanced data augmentation strategies"""
    
    @staticmethod
    def get_training_generator():
        """Enhanced training data generator with medical image specific augmentations"""
        return ImageDataGenerator(
            rescale=1./255,
            rotation_range=30,          # Increased rotation
            width_shift_range=0.2,      # Increased shift
            height_shift_range=0.2,
            shear_range=0.2,            # Increased shear
            zoom_range=0.2,             # Increased zoom
            horizontal_flip=True,
            vertical_flip=True,         # Added vertical flip for medical images
            brightness_range=[0.8, 1.2], # Brightness variation
            channel_shift_range=0.1,    # Color channel shifts
            fill_mode='nearest',
            validation_split=0.2        # 20% for validation
        )
    
    @staticmethod
    def get_test_generator():
        """Test data generator (only rescaling)"""
        return ImageDataGenerator(rescale=1./255)

class ImprovedModelArchitectures:
    """Collection of improved model architectures"""
    
    @staticmethod
    def create_efficientnet_model(input_shape=(150, 150, 3), num_classes=1):
        """EfficientNet-based transfer learning model"""
        base_model = EfficientNetB0(
            weights='imagenet',
            include_top=False,
            input_shape=input_shape
        )
        
        # Freeze base model initially
        base_model.trainable = False
        
        model = Sequential([
            base_model,
            GlobalAveragePooling2D(),
            BatchNormalization(),
            Dropout(0.5),
            Dense(256, activation='relu'),
            BatchNormalization(),
            Dropout(0.3),
            Dense(128, activation='relu'),
            Dropout(0.2),
            Dense(num_classes, activation='sigmoid')
        ])
        
        return model, base_model
    
    @staticmethod
    def create_resnet_model(input_shape=(150, 150, 3), num_classes=1):
        """ResNet50V2-based transfer learning model"""
        base_model = ResNet50V2(
            weights='imagenet',
            include_top=False,
            input_shape=input_shape
        )
        
        base_model.trainable = False
        
        model = Sequential([
            base_model,
            GlobalAveragePooling2D(),
            BatchNormalization(),
            Dropout(0.5),
            Dense(512, activation='relu'),
            BatchNormalization(),
            Dropout(0.4),
            Dense(256, activation='relu'),
            Dropout(0.3),
            Dense(num_classes, activation='sigmoid')
        ])
        
        return model, base_model
    
    @staticmethod
    def create_custom_cnn_model(input_shape=(150, 150, 3), num_classes=1):
        """Improved custom CNN architecture"""
        model = Sequential([
            # First block
            Conv2D(64, (3, 3), activation='relu', input_shape=input_shape, padding='same'),
            BatchNormalization(),
            Conv2D(64, (3, 3), activation='relu', padding='same'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Second block
            Conv2D(128, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(128, (3, 3), activation='relu', padding='same'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Third block
            Conv2D(256, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(256, (3, 3), activation='relu', padding='same'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Fourth block
            Conv2D(512, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(512, (3, 3), activation='relu', padding='same'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Classifier
            GlobalAveragePooling2D(),
            Dense(1024, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(512, activation='relu'),
            Dropout(0.4),
            Dense(256, activation='relu'),
            Dropout(0.3),
            Dense(num_classes, activation='sigmoid')
        ])
        
        return model, None

class TrainingStrategy:
    """Advanced training strategies"""
    
    def __init__(self, model_name='efficientnet'):
        self.model_name = model_name
        self.history = None
        self.model = None
        self.base_model = None
        
    def prepare_data(self):
        """Prepare training and validation data with advanced augmentation"""
        logger.info("Preparing data with advanced augmentation...")
        
        aug = AdvancedDataAugmentation()
        train_datagen = aug.get_training_generator()
        test_datagen = aug.get_test_generator()
        
        # Training data with validation split
        train_generator = train_datagen.flow_from_directory(
            'data/train',
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            subset='training',
            shuffle=True,
            seed=42
        )
        
        validation_generator = train_datagen.flow_from_directory(
            'data/train',
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            subset='validation',
            shuffle=True,
            seed=42
        )
        
        # Test data
        test_generator = test_datagen.flow_from_directory(
            'data/test',
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            shuffle=False
        )
        
        logger.info(f"Training samples: {train_generator.samples}")
        logger.info(f"Validation samples: {validation_generator.samples}")
        logger.info(f"Test samples: {test_generator.samples}")
        
        return train_generator, validation_generator, test_generator
    
    def create_model(self):
        """Create the specified model architecture"""
        logger.info(f"Creating {self.model_name} model...")
        
        architectures = ImprovedModelArchitectures()
        
        if self.model_name == 'efficientnet':
            self.model, self.base_model = architectures.create_efficientnet_model()
        elif self.model_name == 'resnet':
            self.model, self.base_model = architectures.create_resnet_model()
        elif self.model_name == 'custom_cnn':
            self.model, self.base_model = architectures.create_custom_cnn_model()
        else:
            raise ValueError(f"Unknown model architecture: {self.model_name}")
        
        # Compile model
        self.model.compile(
            optimizer=Adam(learning_rate=LEARNING_RATE),
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        logger.info(f"Model created with {self.model.count_params():,} parameters")
        return self.model
    
    def get_callbacks(self, model_path):
        """Get training callbacks"""
        callbacks = [
            ModelCheckpoint(
                model_path,
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                mode='max',
                verbose=1
            ),
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            TensorBoard(
                log_dir=os.path.join(LOG_DIR, f'tensorboard_{self.model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}'),
                histogram_freq=1,
                write_graph=True,
                write_images=True
            )
        ]
        
        return callbacks
    
    def train_model(self, train_gen, val_gen, fine_tune=True):
        """Train the model with optional fine-tuning"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_path = os.path.join(MODEL_DIR, f'malaria_improved_{self.model_name}_{timestamp}.keras')
        
        callbacks = self.get_callbacks(model_path)
        
        logger.info("Starting initial training...")
        
        # Initial training
        history1 = self.model.fit(
            train_gen,
            epochs=EPOCHS // 2,
            validation_data=val_gen,
            callbacks=callbacks,
            verbose=1
        )
        
        # Fine-tuning for transfer learning models
        if fine_tune and self.base_model is not None:
            logger.info("Starting fine-tuning...")
            
            # Unfreeze base model
            self.base_model.trainable = True
            
            # Use lower learning rate for fine-tuning
            self.model.compile(
                optimizer=Adam(learning_rate=LEARNING_RATE / 10),
                loss='binary_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
            
            # Continue training
            history2 = self.model.fit(
                train_gen,
                epochs=EPOCHS // 2,
                validation_data=val_gen,
                callbacks=callbacks,
                verbose=1,
                initial_epoch=len(history1.history['loss'])
            )
            
            # Combine histories
            for key in history1.history:
                history1.history[key].extend(history2.history[key])
        
        self.history = history1
        logger.info(f"Training completed. Model saved to: {model_path}")
        return model_path

    def evaluate_model(self, test_gen, model_path):
        """Comprehensive model evaluation"""
        logger.info("Evaluating model performance...")

        # Load best model
        model = tf.keras.models.load_model(model_path)

        # Evaluate on test set
        test_loss, test_accuracy, test_precision, test_recall = model.evaluate(test_gen, verbose=1)

        # Generate predictions
        test_gen.reset()
        predictions = model.predict(test_gen, verbose=1)
        predicted_classes = (predictions > 0.5).astype(int).flatten()

        # Get true labels
        true_classes = test_gen.classes

        # Calculate F1 score
        from sklearn.metrics import f1_score
        f1 = f1_score(true_classes, predicted_classes)

        # Generate detailed classification report
        class_names = ['Parasitized', 'Uninfected']
        report = classification_report(
            true_classes,
            predicted_classes,
            target_names=class_names,
            output_dict=True
        )

        # Confusion matrix
        cm = confusion_matrix(true_classes, predicted_classes)

        # Save results
        results = {
            'model_name': self.model_name,
            'test_accuracy': float(test_accuracy),
            'test_precision': float(test_precision),
            'test_recall': float(test_recall),
            'test_f1_score': float(f1),
            'test_loss': float(test_loss),
            'classification_report': report,
            'confusion_matrix': cm.tolist(),
            'total_test_samples': len(true_classes),
            'training_history': {k: [float(x) for x in v] for k, v in self.history.history.items()},
            'timestamp': datetime.now().isoformat()
        }

        # Save results to file
        results_file = os.path.join(RESULTS_DIR, f'evaluation_results_{self.model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)

        # Print results
        logger.info("=" * 50)
        logger.info("MODEL EVALUATION RESULTS")
        logger.info("=" * 50)
        logger.info(f"Model: {self.model_name}")
        logger.info(f"Test Accuracy: {test_accuracy:.4f}")
        logger.info(f"Test Precision: {test_precision:.4f}")
        logger.info(f"Test Recall: {test_recall:.4f}")
        logger.info(f"Test F1-Score: {f1:.4f}")
        logger.info(f"Test Loss: {test_loss:.4f}")
        logger.info("\nClassification Report:")
        print(classification_report(true_classes, predicted_classes, target_names=class_names))

        # Plot confusion matrix
        self.plot_confusion_matrix(cm, class_names)

        # Plot training history
        self.plot_training_history()

        return results

    def plot_confusion_matrix(self, cm, class_names):
        """Plot confusion matrix"""
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title(f'Confusion Matrix - {self.model_name}')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, f'confusion_matrix_{self.model_name}.png'), dpi=300)
        plt.close()

    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Accuracy
        axes[0, 0].plot(self.history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()

        # Loss
        axes[0, 1].plot(self.history.history['loss'], label='Training Loss')
        axes[0, 1].plot(self.history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()

        # Precision
        axes[1, 0].plot(self.history.history['precision'], label='Training Precision')
        axes[1, 0].plot(self.history.history['val_precision'], label='Validation Precision')
        axes[1, 0].set_title('Model Precision')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Precision')
        axes[1, 0].legend()

        # Recall
        axes[1, 1].plot(self.history.history['recall'], label='Training Recall')
        axes[1, 1].plot(self.history.history['val_recall'], label='Validation Recall')
        axes[1, 1].set_title('Model Recall')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Recall')
        axes[1, 1].legend()

        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, f'training_history_{self.model_name}.png'), dpi=300)
        plt.close()

def main():
    """Main training function"""
    logger.info("🚀 STARTING IMPROVED MALARIA DETECTION MODEL TRAINING")
    logger.info("=" * 60)

    # Available models
    models_to_train = ['efficientnet', 'resnet', 'custom_cnn']

    best_model = None
    best_accuracy = 0

    for model_name in models_to_train:
        logger.info(f"\n🔥 Training {model_name.upper()} model...")

        try:
            # Initialize training strategy
            trainer = TrainingStrategy(model_name)

            # Prepare data
            train_gen, val_gen, test_gen = trainer.prepare_data()

            # Create and train model
            model = trainer.create_model()
            model_path = trainer.train_model(train_gen, val_gen, fine_tune=True)

            # Evaluate model
            results = trainer.evaluate_model(test_gen, model_path)

            # Track best model
            if results['test_accuracy'] > best_accuracy:
                best_accuracy = results['test_accuracy']
                best_model = model_name

            logger.info(f"✅ {model_name} training completed successfully!")

        except Exception as e:
            logger.error(f"❌ Error training {model_name}: {str(e)}")
            continue

    logger.info("\n" + "=" * 60)
    logger.info("🏆 TRAINING SUMMARY")
    logger.info("=" * 60)
    if best_model:
        logger.info(f"Best Model: {best_model.upper()}")
        logger.info(f"Best Accuracy: {best_accuracy:.4f}")
    logger.info("Check 'training_results/' directory for detailed results and visualizations")

if __name__ == "__main__":
    main()
