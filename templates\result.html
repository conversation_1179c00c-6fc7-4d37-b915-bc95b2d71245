{% extends "base.html" %}

{% block title %}Malaria Erkennung - Ergebnis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- Navigation -->
        <div class="text-center mb-4">
            <ul class="nav nav-pills justify-content-center">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('index') }}">
                        <i class="fas fa-home"></i> Startseite
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('analysis') }}">
                        <i class="fas fa-chart-bar"></i> Algorithmus Analyse
                    </a>
                </li>
            </ul>
        </div>

        <!-- Back Button -->
        <div class="mb-4">
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Neue Analyse
            </a>
        </div>

        {% if result.error %}
            <!-- Error Display -->
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Fehler bei der Analyse</h5>
                <p class="mb-0">{{ result.error }}</p>
            </div>
        {% else %}
            <!-- Results Display -->
            <div class="row">
                <!-- Image Display -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-image"></i> Analysiertes Bild</h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="{{ url_for('static', filename='uploads/' + result.filename) }}" 
                                 class="img-fluid preview-image" 
                                 alt="Analysiertes Bild">
                            <div class="mt-3">
                                <small class="text-muted">
                                    <strong>Datei:</strong> {{ result.original_filename }}<br>
                                    <strong>Hochgeladen:</strong> {{ result.upload_time }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Display -->
                <div class="col-md-6">
                    <div class="card result-card {% if 'Parasitized' in result.prediction or 'Infiziert' in result.prediction %}result-positive{% else %}result-negative{% endif %}">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> Analyseergebnis</h5>
                        </div>
                        <div class="card-body">
                            <!-- Prediction Result -->
                            <div class="text-center mb-4">
                                {% if 'Parasitized' in result.prediction or 'Infiziert' in result.prediction %}
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                                        <h4>{{ result.prediction }}</h4>
                                        <p class="mb-0">Malaria-Parasiten erkannt</p>
                                    </div>
                                {% else %}
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                                        <h4>{{ result.prediction }}</h4>
                                        <p class="mb-0">Keine Malaria-Parasiten erkannt</p>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Confidence Display -->
                            <div class="mb-4">
                                <h6>Konfidenz der Vorhersage</h6>
                                <div class="confidence-bar mb-2">
                                    <div class="confidence-fill 
                                        {% if result.confidence >= 0.8 %}confidence-high
                                        {% elif result.confidence >= 0.6 %}confidence-medium
                                        {% else %}confidence-low{% endif %}" 
                                         style="width: {{ (result.confidence * 100)|round(1) }}%">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small>0%</small>
                                    <strong>{{ (result.confidence * 100)|round(1) }}%</strong>
                                    <small>100%</small>
                                </div>
                            </div>

                            <!-- Technical Details -->
                            <div class="border-top pt-3">
                                <h6>Technische Details</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Wahrscheinlichkeit:</small><br>
                                        <strong>{{ (result.probability * 100)|round(2) }}%</strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Konfidenz:</small><br>
                                        <strong>{{ (result.confidence * 100)|round(2) }}%</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Interpretation Guide -->
                    <div class="card mt-3">
                        <div class="card-body">
                            <h6><i class="fas fa-info-circle"></i> Interpretation</h6>
                            {% if result.confidence >= 0.8 %}
                                <div class="alert alert-info">
                                    <strong>Hohe Konfidenz:</strong> Das Modell ist sehr sicher in seiner Vorhersage.
                                </div>
                            {% elif result.confidence >= 0.6 %}
                                <div class="alert alert-warning">
                                    <strong>Mittlere Konfidenz:</strong> Das Modell ist mäßig sicher. Eine weitere Überprüfung wird empfohlen.
                                </div>
                            {% else %}
                                <div class="alert alert-danger">
                                    <strong>Niedrige Konfidenz:</strong> Das Modell ist unsicher. Eine professionelle Diagnose ist erforderlich.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demo Mode Notice -->
            {% if result.demo_mode %}
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle"></i> Demo-Modus</h6>
                <p class="mb-0">
                    <strong>Hinweis:</strong> Diese Anwendung läuft im Demo-Modus mit simulierten Ergebnissen,
                    da noch kein trainiertes Modell geladen ist. Die Vorhersagen basieren auf einer
                    Demonstration der Benutzeroberfläche und sind nicht real.
                </p>
            </div>
            {% endif %}

            <!-- Medical Disclaimer -->
            <div class="alert alert-warning mt-4">
                <h6><i class="fas fa-exclamation-triangle"></i> Medizinischer Haftungsausschluss</h6>
                <p class="mb-0">
                    <strong>Wichtig:</strong> Dieses Ergebnis dient nur zu Demonstrationszwecken und ersetzt
                    keine professionelle medizinische Diagnose. Bei Verdacht auf Malaria oder anderen
                    gesundheitlichen Problemen konsultieren Sie bitte einen qualifizierten Arzt oder
                    ein medizinisches Labor für eine ordnungsgemäße Diagnose und Behandlung.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="text-center mt-4">
                <a href="{{ url_for('index') }}" class="btn btn-custom btn-lg me-3">
                    <i class="fas fa-plus"></i> Weitere Analyse
                </a>
                <button class="btn btn-outline-secondary" onclick="window.print()">
                    <i class="fas fa-print"></i> Ergebnis drucken
                </button>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .btn, .alert-warning:last-of-type {
            display: none !important;
        }
        
        .main-container {
            box-shadow: none;
            background: white;
        }
        
        .header {
            background: #2c3e50 !important;
            -webkit-print-color-adjust: exact;
        }
    }
    
    .confidence-fill {
        transition: width 1s ease-in-out;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Animate confidence bar on page load
    document.addEventListener('DOMContentLoaded', function() {
        const confidenceFill = document.querySelector('.confidence-fill');
        if (confidenceFill) {
            const targetWidth = confidenceFill.style.width;
            confidenceFill.style.width = '0%';
            
            setTimeout(() => {
                confidenceFill.style.width = targetWidth;
            }, 500);
        }
    });

    // Add some interactivity
    function shareResult() {
        if (navigator.share) {
            navigator.share({
                title: 'Malaria Analyse Ergebnis',
                text: 'Ergebnis der Malaria-Analyse: {{ result.prediction }}',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Link wurde in die Zwischenablage kopiert!');
            });
        }
    }
</script>
{% endblock %}