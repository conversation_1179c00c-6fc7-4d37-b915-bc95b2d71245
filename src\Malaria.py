# """
# Malaria-Erkennungssystem mit Deep Learning
# Verbessertes CNN-Modell zur Erkennung von Malaria in Blutzellen-Bildern
# mit Transfer Learning, erweiterten Metriken und verbesserter Fehlerbehandlung.
# """
#
# import os
# import numpy as np
# import logging
# from datetime import datetime
# import tensorflow as tf
# from tensorflow.keras.models import Sequential, save_model, load_model
# from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, BatchNormalization
# from tensorflow.keras.preprocessing.image import ImageDataGenerator
# from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau, TensorBoard
# from tensorflow.keras.applications import VGG16
# from tensorflow.keras.metrics import Precision, Recall, AUC
# from sklearn.metrics import classification_report, confusion_matrix
# from PIL import Image
# import matplotlib.pyplot as plt
# import seaborn as sns
# import warnings
# warnings.filterwarnings("ignore")
#
# # Logger konfigurieren
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('malaria_detection.log'),
#         logging.StreamHandler()
#     ]
# )
# logger = logging.getLogger(__name__)
#
# # Konstanten
# IMAGE_SIZE = 128
# BATCH_SIZE = 32
# EPOCHS = 30
# LEARNING_RATE = 0.001
#
# # Pfade definieren
# BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# DATASET_DIR = os.path.join(BASE_DIR, 'data')
# MODEL_DIR = os.path.join(BASE_DIR, 'models')
# LOG_DIR = os.path.join(BASE_DIR, 'logs')
#
# for directory in [MODEL_DIR, LOG_DIR]:
#     os.makedirs(directory, exist_ok=True)
#
# def create_model():
#     """Erweitertes CNN-Modell mit Transfer Learning erstellen"""
#     # VGG16 als Base Model
#     base_model = VGG16(
#         weights='imagenet',
#         include_top=False,
#         input_shape=(IMAGE_SIZE, IMAGE_SIZE, 3)
#     )
#
#     # Base Model einfrieren
#     base_model.trainable = False
#
#     model = Sequential([
#         base_model,
#         Conv2D(128, 3, activation='relu', padding='same'),
#         BatchNormalization(),
#         MaxPooling2D(),
#         Dropout(0.3),
#
#         Conv2D(64, 3, activation='relu', padding='same'),
#         BatchNormalization(),
#         MaxPooling2D(),
#         Dropout(0.3),
#
#         Flatten(),
#         Dense(256, activation='relu'),
#         BatchNormalization(),
#         Dropout(0.5),
#         Dense(128, activation='relu'),
#         BatchNormalization(),
#         Dropout(0.5),
#         Dense(1, activation='sigmoid')
#     ])
#
#     # Modell kompilieren
#     optimizer = tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE)
#     model.compile(
#         optimizer=optimizer,
#         loss='binary_crossentropy',
#         metrics=['accuracy', Precision(), Recall(), AUC()]
#     )
#
#     return model
#
# def prepare_data():
#     """Erweiterte Datenvorbereitung mit zusätzlicher Augmentierung"""
#     train_datagen = ImageDataGenerator(
#         rescale=1./255,
#         rotation_range=40,
#         width_shift_range=0.2,
#         height_shift_range=0.2,
#         shear_range=0.2,
#         zoom_range=0.2,
#         horizontal_flip=True,
#         vertical_flip=True,
#         fill_mode='nearest',
#         validation_split=0.2
#     )
#
#     test_datagen = ImageDataGenerator(rescale=1./255)
#
#     try:
#         logger.info("Lade Trainingsdaten...")
#         training_set = train_datagen.flow_from_directory(
#             os.path.join(DATASET_DIR, 'train'),
#             target_size=(IMAGE_SIZE, IMAGE_SIZE),
#             batch_size=BATCH_SIZE,
#             class_mode='binary',
#             subset='training'
#         )
#
#         validation_set = train_datagen.flow_from_directory(
#             os.path.join(DATASET_DIR, 'train'),
#             target_size=(IMAGE_SIZE, IMAGE_SIZE),
#             batch_size=BATCH_SIZE,
#             class_mode='binary',
#             subset='validation'
#         )
#
#         test_set = test_datagen.flow_from_directory(
#             os.path.join(DATASET_DIR, 'test'),
#             target_size=(IMAGE_SIZE, IMAGE_SIZE),
#             batch_size=BATCH_SIZE,
#             class_mode='binary'
#         )
#
#         return training_set, validation_set, test_set
#
#     except Exception as e:
#         logger.error(f"Fehler beim Laden der Daten: {str(e)}")
#         raise
#
# def plot_training_history(history, save_path):
#     """Trainingsmetriken visualisieren"""
#     metrics = ['loss', 'accuracy', 'precision', 'recall', 'auc']
#     plt.figure(figsize=(15, 10))
#
#     for i, metric in enumerate(metrics, 1):
#         plt.subplot(2, 3, i)
#         plt.plot(history.history[metric], label=f'Training {metric}')
#         plt.plot(history.history[f'val_{metric}'], label=f'Validation {metric}')
#         plt.title(f'Model {metric}')
#         plt.xlabel('Epoch')
#         plt.ylabel(metric)
#         plt.legend()
#
#     plt.tight_layout()
#     plt.savefig(save_path)
#     plt.close()
#
# def train_model(model, training_set, validation_set):
#     """Erweitertes Modelltraining mit zusätzlichen Callbacks"""
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     model_path = os.path.join(MODEL_DIR, f'malaria_model_{timestamp}.h5')
#
#     callbacks = [
#         EarlyStopping(
#             monitor='val_loss',
#             patience=5,
#             restore_best_weights=True
#         ),
#         ModelCheckpoint(
#             model_path,
#             monitor='val_accuracy',
#             save_best_only=True
#         ),
#         ReduceLROnPlateau(
#             monitor='val_loss',
#             factor=0.2,
#             patience=3,
#             min_lr=1e-6
#         ),
#         TensorBoard(
#             log_dir=os.path.join(LOG_DIR, timestamp),
#             histogram_freq=1
#         )
#     ]
#
#     logger.info("Starte Training...")
#     history = model.fit(
#         training_set,
#         steps_per_epoch=training_set.samples // BATCH_SIZE,
#         epochs=EPOCHS,
#         validation_data=validation_set,
#         validation_steps=validation_set.samples // BATCH_SIZE,
#         callbacks=callbacks
#     )
#
#     # Trainingsmetriken plotten
#     plot_path = os.path.join(LOG_DIR, f'training_metrics_{timestamp}.png')
#     plot_training_history(history, plot_path)
#
#     logger.info(f"Modell gespeichert unter: {model_path}")
#     logger.info(f"Trainingsmetriken gespeichert unter: {plot_path}")
#
#     return history, model_path
#
# def evaluate_model(model, test_set):
#     """Modell evaluieren und Metriken berechnen"""
#     logger.info("Evaluiere Modell...")
#
#     # Vorhersagen generieren
#     predictions = model.predict(test_set)
#     y_pred = (predictions > 0.5).astype(int)
#     y_true = test_set.classes
#
#     # Confusion Matrix erstellen
#     cm = confusion_matrix(y_true, y_pred)
#     plt.figure(figsize=(8, 6))
#     sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
#     plt.title('Confusion Matrix')
#     plt.ylabel('True Label')
#     plt.xlabel('Predicted Label')
#
#     # Speichern der Confusion Matrix
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     cm_path = os.path.join(LOG_DIR, f'confusion_matrix_{timestamp}.png')
#     plt.savefig(cm_path)
#     plt.close()
#
#     # Klassifikationsbericht erstellen
#     report = classification_report(y_true, y_pred)
#     logger.info("\nKlassifikationsbericht:\n" + report)
#
#     return cm_path
#
# def predict_image(model_path, image_path):
#     """Verbesserte Bildvorhersage mit Fehlerbehandlung"""
#     try:
#         if not os.path.exists(image_path):
#             raise FileNotFoundError(f"Bild nicht gefunden: {image_path}")
#
#         model = load_model(model_path)
#
#         # Bild laden und vorverarbeiten
#         test_image = Image.open(image_path)
#         if test_image.mode != 'RGB':
#             test_image = test_image.convert('RGB')
#
#         test_image = test_image.resize((IMAGE_SIZE, IMAGE_SIZE))
#         test_image = np.array(test_image) / 255.0
#         test_image = np.expand_dims(test_image, axis=0)
#
#         # Vorhersage machen
#         result = model.predict(test_image)
#         prediction = 'Nicht infiziert' if result[0][0] > 0.5 else 'Infiziert'
#         confidence = result[0][0] if result[0][0] > 0.5 else 1 - result[0][0]
#
#         return prediction, confidence
#
#     except Exception as e:
#         logger.error(f"Fehler bei der Vorhersage: {str(e)}")
#         raise
#
# def main():
#     """Hauptfunktion mit erweiterter Fehlerbehandlung"""
#     try:
#         # Überprüfe Datensatz-Verzeichnis
#         train_dir = os.path.join(DATASET_DIR, 'train')
#         test_dir = os.path.join(DATASET_DIR, 'test')
#
#         if not all(os.path.exists(d) for d in [train_dir, test_dir]):
#             raise FileNotFoundError(
#                 f"Erforderliche Verzeichnisse nicht gefunden. "
#                 f"Bitte stelle sicher, dass {train_dir} und {test_dir} existieren."
#             )
#
#         # Modell erstellen und trainieren
#         model = create_model()
#         training_set, validation_set, test_set = prepare_data()
#         history, model_path = train_model(model, training_set, validation_set)
#
#         # Modell evaluieren
#         cm_path = evaluate_model(model, test_set)
#         logger.info(f"Confusion Matrix gespeichert unter: {cm_path}")
#
#         # Optional: Beispiel für eine Vorhersage
#         test_image_path = os.path.join(DATASET_DIR, 'single_prediction', 'test_image.png')
#         if os.path.exists(test_image_path):
#             try:
#                 prediction, confidence = predict_image(model_path, test_image_path)
#                 logger.info(f"Vorhersage: {prediction}")
#                 logger.info(f"Konfidenz: {confidence:.2%}")
#             except Exception as e:
#                 logger.warning(f"Fehler bei der Beispielvorhersage: {str(e)}")
#
#     except Exception as e:
#         logger.error(f"Kritischer Fehler im Hauptprogramm: {str(e)}")
#         raise
#
# if __name__ == "__main__":
#     main()





"""
Malaria-Erkennungssystem mit Deep Learning
Verbessertes CNN-Modell zur Erkennung von Malaria in Blutzellen-Bildern
mit Transfer Learning, erweiterten Metriken und verbesserter Fehlerbehandlung.
"""

import os
import numpy as np
import logging
from datetime import datetime
import tensorflow as tf
from tensorflow.keras.models import Sequential, save_model, load_model
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, BatchNormalization
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau, TensorBoard
from tensorflow.keras.applications import VGG16
from tensorflow.keras.metrics import Precision, Recall, AUC
from sklearn.metrics import classification_report, confusion_matrix
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings("ignore")

# Logger konfigurieren
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('malaria_detection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Konstanten
IMAGE_SIZE = 128
BATCH_SIZE = 32
EPOCHS = 30
LEARNING_RATE = 0.001

# Pfade definieren
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATASET_DIR = os.path.join(BASE_DIR, 'data')
MODEL_DIR = os.path.join(BASE_DIR, 'models')
LOG_DIR = os.path.join(BASE_DIR, 'logs')

for directory in [MODEL_DIR, LOG_DIR]:
    os.makedirs(directory, exist_ok=True)

def create_model():
    """Erweitertes CNN-Modell mit Transfer Learning erstellen"""
    # VGG16 als Base Model
    base_model = VGG16(
        weights='imagenet',
        include_top=False,
        input_shape=(IMAGE_SIZE, IMAGE_SIZE, 3)
    )

    # Base Model einfrieren
    base_model.trainable = False

    model = Sequential([
        base_model,
        Conv2D(128, 3, activation='relu', padding='same'),
        BatchNormalization(),
        MaxPooling2D(),
        Dropout(0.3),

        Conv2D(64, 3, activation='relu', padding='same'),
        BatchNormalization(),
        MaxPooling2D(),
        Dropout(0.3),

        Flatten(),
        Dense(256, activation='relu'),
        BatchNormalization(),
        Dropout(0.5),
        Dense(128, activation='relu'),
        BatchNormalization(),
        Dropout(0.5),
        Dense(1, activation='sigmoid')
    ])

    # Modell kompilieren
    optimizer = tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE)
    model.compile(
        optimizer=optimizer,
        loss='binary_crossentropy',
        metrics=['accuracy', Precision(), Recall(), AUC()]
    )

    return model

def prepare_data():
    """Erweiterte Datenvorbereitung mit zusätzlicher Augmentierung"""
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=40,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=True,
        fill_mode='nearest',
        validation_split=0.2
    )

    test_datagen = ImageDataGenerator(rescale=1./255)

    try:
        logger.info("Lade Trainingsdaten...")
        training_set = train_datagen.flow_from_directory(
            os.path.join(DATASET_DIR, 'train'),
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            subset='training'
        )

        validation_set = train_datagen.flow_from_directory(
            os.path.join(DATASET_DIR, 'train'),
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary',
            subset='validation'
        )

        test_set = test_datagen.flow_from_directory(
            os.path.join(DATASET_DIR, 'test'),
            target_size=(IMAGE_SIZE, IMAGE_SIZE),
            batch_size=BATCH_SIZE,
            class_mode='binary'
        )

        return training_set, validation_set, test_set

    except Exception as e:
        logger.error(f"Fehler beim Laden der Daten: {str(e)}")
        raise

def plot_training_history(history, save_path):
    """Trainingsmetriken visualisieren"""
    metrics = ['loss', 'accuracy', 'precision', 'recall', 'auc']
    plt.figure(figsize=(15, 10))

    for i, metric in enumerate(metrics, 1):
        plt.subplot(2, 3, i)
        plt.plot(history.history[metric], label=f'Training {metric}')
        plt.plot(history.history[f'val_{metric}'], label=f'Validation {metric}')
        plt.title(f'Model {metric}')
        plt.xlabel('Epoch')
        plt.ylabel(metric)
        plt.legend()

    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def train_model(model, training_set, validation_set):
    """Erweitertes Modelltraining mit zusätzlichen Callbacks"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(MODEL_DIR, f'malaria_model_{timestamp}.h5')

    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=5,
            restore_best_weights=True
        ),
        ModelCheckpoint(
            model_path,
            monitor='val_accuracy',
            save_best_only=True
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=3,
            min_lr=1e-6
        ),
        TensorBoard(
            log_dir=os.path.join(LOG_DIR, timestamp),
            histogram_freq=1
        )
    ]

    logger.info("Starte Training...")
    history = model.fit(
        training_set,
        steps_per_epoch=training_set.samples // BATCH_SIZE,
        epochs=EPOCHS,
        validation_data=validation_set,
        validation_steps=validation_set.samples // BATCH_SIZE,
        callbacks=callbacks
    )

    # Trainingsmetriken plotten
    plot_path = os.path.join(LOG_DIR, f'training_metrics_{timestamp}.png')
    plot_training_history(history, plot_path)

    logger.info(f"Modell gespeichert unter: {model_path}")
    logger.info(f"Trainingsmetriken gespeichert unter: {plot_path}")

    return history, model_path

def evaluate_model(model, test_set):
    """Modell evaluieren und Metriken berechnen"""
    logger.info("Evaluiere Modell...")

    # Vorhersagen generieren
    predictions = model.predict(test_set)
    y_pred = (predictions > 0.5).astype(int)
    y_true = test_set.classes

    # Confusion Matrix erstellen
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')

    # Speichern der Confusion Matrix
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    cm_path = os.path.join(LOG_DIR, f'confusion_matrix_{timestamp}.png')
    plt.savefig(cm_path)
    plt.close()

    # Klassifikationsbericht erstellen
    report = classification_report(y_true, y_pred)
    logger.info("\nKlassifikationsbericht:\n" + report)

    return cm_path

def predict_image(model_path, image_path):
    """Verbesserte Bildvorhersage mit Fehlerbehandlung"""
    try:
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Bild nicht gefunden: {image_path}")

        model = load_model(model_path)

        # Bild laden und vorverarbeiten
        test_image = Image.open(image_path)
        if test_image.mode != 'RGB':
            test_image = test_image.convert('RGB')

        test_image = test_image.resize((IMAGE_SIZE, IMAGE_SIZE))
        test_image = np.array(test_image) / 255.0
        test_image = np.expand_dims(test_image, axis=0)

        # Vorhersage machen
        result = model.predict(test_image)
        prediction = 'Nicht infiziert' if result[0][0] > 0.5 else 'Infiziert'
        confidence = result[0][0] if result[0][0] > 0.5 else 1 - result[0][0]

        return prediction, confidence

    except Exception as e:
        logger.error(f"Fehler bei der Vorhersage: {str(e)}")
        raise

def main():
    """Hauptfunktion mit erweiterter Fehlerbehandlung"""
    try:
        # Überprüfe Datensatz-Verzeichnis
        train_dir = os.path.join(DATASET_DIR, 'train')
        test_dir = os.path.join(DATASET_DIR, 'test')

        if not all(os.path.exists(d) for d in [train_dir, test_dir]):
            raise FileNotFoundError(
                f"Erforderliche Verzeichnisse nicht gefunden. "
                f"Bitte stelle sicher, dass {train_dir} und {test_dir} existieren."
            )

        # Modell erstellen und trainieren
        model = create_model()
        training_set, validation_set, test_set = prepare_data()
        history, model_path = train_model(model, training_set, validation_set)

        # Modell evaluieren
        cm_path = evaluate_model(model, test_set)
        logger.info(f"Confusion Matrix gespeichert unter: {cm_path}")

        # Optional: Beispiel für eine Vorhersage
        test_image_path = os.path.join(DATASET_DIR, 'single_prediction', 'test_image.png')
        if os.path.exists(test_image_path):
            try:
                prediction, confidence = predict_image(model_path, test_image_path)
                logger.info(f"Vorhersage: {prediction}")
                logger.info(f"Konfidenz: {confidence:.2%}")
            except Exception as e:
                logger.warning(f"Fehler bei der Beispielvorhersage: {str(e)}")

    except Exception as e:
        logger.error(f"Kritischer Fehler im Hauptprogramm: {str(e)}")
        raise

if __name__ == "__main__":
    main()




