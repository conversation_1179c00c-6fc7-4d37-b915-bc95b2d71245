#!/usr/bin/env python3
"""
Advanced Malaria Detection Model Training
Implements class balancing and advanced techniques for better Parasitized detection
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.applications import EfficientNetB0
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Dropout, BatchNormalization
from tensorflow.keras.models import Sequential
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import Model<PERSON>heckpoint, EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.metrics import classification_report, confusion_matrix, f1_score
from sklearn.utils.class_weight import compute_class_weight
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import logging

# Configuration
IMAGE_SIZE = 150
BATCH_SIZE = 16  # Reduced for better gradient updates
EPOCHS = 50      # Increased for better learning
LEARNING_RATE = 0.00005  # Lower learning rate
MODEL_DIR = 'models'
LOG_DIR = 'logs'
RESULTS_DIR = 'training_results'

# Create directories
for directory in [MODEL_DIR, LOG_DIR, RESULTS_DIR]:
    os.makedirs(directory, exist_ok=True)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_advanced_model():
    """Create advanced EfficientNet-based model with better architecture"""
    logger.info("Creating advanced EfficientNet model...")
    
    # Load pre-trained EfficientNet
    base_model = EfficientNetB0(
        weights='imagenet',
        include_top=False,
        input_shape=(IMAGE_SIZE, IMAGE_SIZE, 3)
    )
    
    # Freeze base model initially
    base_model.trainable = False
    
    # Build improved model with more capacity
    model = Sequential([
        base_model,
        GlobalAveragePooling2D(),
        BatchNormalization(),
        Dropout(0.6),  # Increased dropout
        Dense(512, activation='relu'),  # Larger dense layer
        BatchNormalization(),
        Dropout(0.5),
        Dense(256, activation='relu'),
        BatchNormalization(),
        Dropout(0.4),
        Dense(128, activation='relu'),
        Dropout(0.3),
        Dense(1, activation='sigmoid')
    ])
    
    logger.info(f"Model created with improved architecture")
    return model, base_model

def prepare_balanced_data():
    """Prepare data with class balancing techniques"""
    logger.info("Preparing data with class balancing...")
    
    # More aggressive augmentation for minority class
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=40,      # Increased
        width_shift_range=0.3,  # Increased
        height_shift_range=0.3, # Increased
        shear_range=0.3,        # Increased
        zoom_range=0.3,         # Increased
        horizontal_flip=True,
        vertical_flip=True,
        brightness_range=[0.7, 1.3],  # More variation
        channel_shift_range=0.2,      # Increased
        fill_mode='nearest',
        validation_split=0.2
    )
    
    # Test data generator
    test_datagen = ImageDataGenerator(rescale=1./255)
    
    # Create generators
    train_generator = train_datagen.flow_from_directory(
        'data/train',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        subset='training',
        shuffle=True,
        seed=42
    )
    
    validation_generator = train_datagen.flow_from_directory(
        'data/train',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        subset='validation',
        shuffle=True,
        seed=42
    )
    
    test_generator = test_datagen.flow_from_directory(
        'data/test',
        target_size=(IMAGE_SIZE, IMAGE_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='binary',
        shuffle=False
    )
    
    # Calculate class weights for balancing
    train_labels = train_generator.classes
    class_weights = compute_class_weight(
        'balanced',
        classes=np.unique(train_labels),
        y=train_labels
    )
    class_weight_dict = dict(enumerate(class_weights))
    
    logger.info(f"Training samples: {train_generator.samples}")
    logger.info(f"Validation samples: {validation_generator.samples}")
    logger.info(f"Test samples: {test_generator.samples}")
    logger.info(f"Class weights: {class_weight_dict}")
    
    return train_generator, validation_generator, test_generator, class_weight_dict

def train_advanced_model():
    """Train the advanced model with class balancing"""
    logger.info("🚀 Starting advanced model training with class balancing...")
    
    # Prepare data
    train_gen, val_gen, test_gen, class_weights = prepare_balanced_data()
    
    # Create model
    model, base_model = create_advanced_model()
    
    # Compile with class-aware loss
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE),
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )
    
    # Setup callbacks
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(MODEL_DIR, f'malaria_advanced_balanced_{timestamp}.keras')
    
    callbacks = [
        ModelCheckpoint(
            model_path,
            monitor='val_f1_score',  # Monitor F1 score instead of accuracy
            save_best_only=True,
            save_weights_only=False,
            mode='max',
            verbose=1
        ),
        EarlyStopping(
            monitor='val_loss',
            patience=12,  # Increased patience
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.3,   # More aggressive reduction
            patience=6,   # Reduced patience
            min_lr=1e-8,
            verbose=1
        )
    ]
    
    # Custom F1 score metric
    def f1_score_metric(y_true, y_pred):
        def recall(y_true, y_pred):
            true_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_true * y_pred, 0, 1)))
            possible_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_true, 0, 1)))
            recall = true_positives / (possible_positives + tf.keras.backend.epsilon())
            return recall

        def precision(y_true, y_pred):
            true_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_true * y_pred, 0, 1)))
            predicted_positives = tf.keras.backend.sum(tf.keras.backend.round(tf.keras.backend.clip(y_pred, 0, 1)))
            precision = true_positives / (predicted_positives + tf.keras.backend.epsilon())
            return precision

        precision_val = precision(y_true, y_pred)
        recall_val = recall(y_true, y_pred)
        return 2*((precision_val*recall_val)/(precision_val+recall_val+tf.keras.backend.epsilon()))
    
    # Recompile with F1 metric
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE),
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall', f1_score_metric]
    )
    
    # Phase 1: Train with frozen base and class weights
    logger.info("Phase 1: Training with frozen base model and class balancing...")
    history1 = model.fit(
        train_gen,
        epochs=EPOCHS // 2,
        validation_data=val_gen,
        callbacks=callbacks,
        class_weight=class_weights,  # Apply class weights
        verbose=1
    )
    
    # Phase 2: Fine-tuning with unfrozen base
    logger.info("Phase 2: Fine-tuning with unfrozen base model...")
    base_model.trainable = True
    
    # Freeze early layers, only train top layers
    for layer in base_model.layers[:-20]:  # Freeze all but last 20 layers
        layer.trainable = False
    
    # Lower learning rate for fine-tuning
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE / 5),  # Even lower LR
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall', f1_score_metric]
    )
    
    history2 = model.fit(
        train_gen,
        epochs=EPOCHS // 2,
        validation_data=val_gen,
        callbacks=callbacks,
        class_weight=class_weights,  # Continue using class weights
        verbose=1,
        initial_epoch=len(history1.history['loss'])
    )
    
    # Combine histories
    for key in history1.history:
        if key in history2.history:
            history1.history[key].extend(history2.history[key])
    
    logger.info(f"Advanced training completed! Model saved to: {model_path}")
    
    # Evaluate model
    evaluate_advanced_model(model_path, test_gen, history1)
    
    return model_path

def evaluate_advanced_model(model_path, test_gen, history):
    """Evaluate the advanced trained model"""
    logger.info("Evaluating advanced model performance...")
    
    # Load best model
    model = tf.keras.models.load_model(model_path, compile=False)
    
    # Recompile for evaluation
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE),
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )
    
    # Evaluate on test set
    test_loss, test_accuracy, test_precision, test_recall = model.evaluate(test_gen, verbose=1)
    
    # Generate predictions with different thresholds
    test_gen.reset()
    predictions = model.predict(test_gen, verbose=1)
    
    # Try different thresholds to optimize F1 score
    thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
    best_threshold = 0.5
    best_f1 = 0
    
    true_classes = test_gen.classes
    
    for threshold in thresholds:
        predicted_classes = (predictions > threshold).astype(int).flatten()
        f1 = f1_score(true_classes, predicted_classes)
        logger.info(f"Threshold {threshold}: F1-Score = {f1:.4f}")
        
        if f1 > best_f1:
            best_f1 = f1
            best_threshold = threshold
    
    # Use best threshold for final predictions
    predicted_classes = (predictions > best_threshold).astype(int).flatten()
    final_f1 = f1_score(true_classes, predicted_classes)
    
    # Classification report
    class_names = ['Parasitized', 'Uninfected']
    report = classification_report(
        true_classes, 
        predicted_classes, 
        target_names=class_names,
        output_dict=True
    )
    
    # Confusion matrix
    cm = confusion_matrix(true_classes, predicted_classes)
    
    # Calculate additional metrics
    sensitivity = cm[0][0] / (cm[0][0] + cm[0][1]) if (cm[0][0] + cm[0][1]) > 0 else 0
    specificity = cm[1][1] / (cm[1][1] + cm[1][0]) if (cm[1][1] + cm[1][0]) > 0 else 0
    
    # Print results
    logger.info("=" * 60)
    logger.info("🎉 ADVANCED MODEL EVALUATION RESULTS")
    logger.info("=" * 60)
    logger.info(f"Best Threshold: {best_threshold}")
    logger.info(f"Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
    logger.info(f"Test Precision: {test_precision:.4f} ({test_precision*100:.2f}%)")
    logger.info(f"Test Recall: {test_recall:.4f} ({test_recall*100:.2f}%)")
    logger.info(f"Optimized F1-Score: {final_f1:.4f} ({final_f1*100:.2f}%)")
    logger.info(f"Sensitivity (Parasitized Detection): {sensitivity:.4f} ({sensitivity*100:.2f}%)")
    logger.info(f"Specificity (Uninfected Detection): {specificity:.4f} ({specificity*100:.2f}%)")
    logger.info(f"Test Loss: {test_loss:.4f}")
    
    logger.info("\n📊 Detailed Classification Report:")
    print(classification_report(true_classes, predicted_classes, target_names=class_names))
    
    logger.info("\n📈 Confusion Matrix:")
    logger.info(f"True Positives (Parasitized correctly identified): {cm[0][0]}")
    logger.info(f"False Negatives (Parasitized missed): {cm[0][1]}")
    logger.info(f"False Positives (Uninfected misclassified): {cm[1][0]}")
    logger.info(f"True Negatives (Uninfected correctly identified): {cm[1][1]}")
    
    # Save results
    results = {
        'model_name': 'advanced_balanced_efficientnet',
        'best_threshold': float(best_threshold),
        'test_accuracy': float(test_accuracy),
        'test_precision': float(test_precision),
        'test_recall': float(test_recall),
        'optimized_f1_score': float(final_f1),
        'test_loss': float(test_loss),
        'sensitivity': float(sensitivity),
        'specificity': float(specificity),
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'total_test_samples': len(true_classes),
        'timestamp': datetime.now().isoformat()
    }
    
    # Save to file
    results_file = f'training_results/advanced_model_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Plot improved confusion matrix
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
               xticklabels=class_names, yticklabels=class_names,
               cbar_kws={'label': 'Count'})
    plt.title(f'Advanced Model - Confusion Matrix\n(Threshold: {best_threshold}, F1: {final_f1:.3f})')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    
    # Add percentage annotations
    total = cm.sum()
    for i in range(2):
        for j in range(2):
            percentage = cm[i, j] / total * 100
            plt.text(j + 0.5, i + 0.7, f'({percentage:.1f}%)', 
                    ha='center', va='center', fontsize=10, color='red')
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'advanced_confusion_matrix.png'), dpi=300)
    plt.close()
    
    logger.info(f"📊 Advanced evaluation complete! Results saved to: {results_file}")
    
    # Compare with previous model
    logger.info("\n🔄 COMPARISON WITH PREVIOUS MODEL:")
    logger.info("=" * 50)
    logger.info("Previous Model:")
    logger.info("  - F1-Score: 66.46%")
    logger.info("  - Parasitized Detection: 0%")
    logger.info("  - Uninfected Detection: 100%")
    logger.info()
    logger.info("Advanced Model:")
    logger.info(f"  - F1-Score: {final_f1*100:.2f}% (Change: {(final_f1-0.6646)*100:+.2f}%)")
    logger.info(f"  - Parasitized Detection: {sensitivity*100:.2f}% (Change: {sensitivity*100:+.2f}%)")
    logger.info(f"  - Uninfected Detection: {specificity*100:.2f}% (Change: {(specificity-1.0)*100:+.2f}%)")
    
    return results

def main():
    """Main function"""
    logger.info("🔬 ADVANCED MALARIA DETECTION MODEL TRAINING")
    logger.info("=" * 60)
    logger.info("Features: Class Balancing, Optimized Thresholds, Enhanced Architecture")
    
    try:
        model_path = train_advanced_model()
        logger.info("✅ Advanced training completed successfully!")
        logger.info(f"📁 Model saved to: {model_path}")
        logger.info("📊 Check 'training_results/' directory for detailed results")
        
    except Exception as e:
        logger.error(f"❌ Advanced training failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
