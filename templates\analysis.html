{% extends "base.html" %}

{% block title %}Algorithmus Analyse - Malaria Erkennung{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin: 15px 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-value {
        font-size: 3rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .metric-label {
        font-size: 1.2rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .progress-custom {
        height: 20px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    
    .progress-bar-custom {
        height: 100%;
        border-radius: 10px;
        transition: width 0.8s ease;
        background: linear-gradient(90deg, #27ae60, #2ecc71);
    }
    
    .confusion-matrix {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
    }
    
    .matrix-cell {
        border: 2px solid #ecf0f1;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin: 5px;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .matrix-cell.correct {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
    }
    
    .matrix-cell.incorrect {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
    }
    
    .info-badge {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin: 5px;
        display: inline-block;
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 300;
        margin: 30px 0 20px 0;
        text-align: center;
    }
    
    .nav-pills .nav-link {
        border-radius: 25px;
        margin: 0 5px;
        transition: all 0.3s ease;
    }
    
    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Navigation -->
    <div class="text-center mb-4">
        <ul class="nav nav-pills justify-content-center">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Startseite
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('analysis') }}">
                    <i class="fas fa-chart-bar"></i> Algorithmus Analyse
                </a>
            </li>
        </ul>
    </div>

    <h2 class="section-title">
        <i class="fas fa-brain"></i> Algorithmus Performance Analyse
    </h2>
    
    <!-- Main Metrics -->
    <div class="row">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">Genauigkeit</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.accuracy * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.accuracy * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">Precision</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.precision.average * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.precision.average * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">Recall</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.recall.average * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.recall.average * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">F1-Score</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.f1_score.average * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.f1_score.average * 100 }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Metrics -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h4><i class="fas fa-microscope"></i> Klassen-spezifische Metriken</h4>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Klasse</th>
                            <th>Precision</th>
                            <th>Recall</th>
                            <th>F1-Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Parasitized</strong></td>
                            <td>{{ "%.2f"|format(metrics.precision.parasitized * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.recall.parasitized * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.f1_score.parasitized * 100) }}%</td>
                        </tr>
                        <tr>
                            <td><strong>Uninfected</strong></td>
                            <td>{{ "%.2f"|format(metrics.precision.uninfected * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.recall.uninfected * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.f1_score.uninfected * 100) }}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="chart-container">
                <h4><i class="fas fa-heartbeat"></i> Zusätzliche Metriken</h4>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h5>Sensitivität</h5>
                            <div class="metric-value text-primary">{{ "%.2f"|format(metrics.sensitivity * 100) }}%</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5>Spezifität</h5>
                            <div class="metric-value text-success">{{ "%.2f"|format(metrics.specificity * 100) }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confusion Matrix -->
    <div class="confusion-matrix">
        <h4 class="text-center mb-4"><i class="fas fa-table"></i> Konfusionsmatrix</h4>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th rowspan="2" class="align-middle text-center">Tatsächlich</th>
                            <th colspan="2" class="text-center">Vorhergesagt</th>
                        </tr>
                        <tr>
                            <th class="text-center">Parasitized</th>
                            <th class="text-center">Uninfected</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th>Parasitized</th>
                            <td class="matrix-cell correct">{{ metrics.confusion_matrix.true_positive }}</td>
                            <td class="matrix-cell incorrect">{{ metrics.confusion_matrix.false_negative }}</td>
                        </tr>
                        <tr>
                            <th>Uninfected</th>
                            <td class="matrix-cell incorrect">{{ metrics.confusion_matrix.false_positive }}</td>
                            <td class="matrix-cell correct">{{ metrics.confusion_matrix.true_negative }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Model Information -->
    <div class="chart-container">
        <h4><i class="fas fa-info-circle"></i> Modell Informationen</h4>
        <div class="row">
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-database"></i> Gesamt Samples: {{ metrics.total_samples }}
                </span>
            </div>
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-clock"></i> Training Zeit: {{ metrics.training_time }}
                </span>
            </div>
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-hdd"></i> Modell Größe: {{ metrics.model_size }}
                </span>
            </div>
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-calendar"></i> Letztes Update: {{ metrics.last_updated }}
                </span>
            </div>
        </div>
    </div>

    <!-- Performance Interpretation -->
    <div class="chart-container">
        <h4><i class="fas fa-lightbulb"></i> Performance Interpretation</h4>
        <div class="row">
            <div class="col-md-6">
                <h5>Stärken des Modells:</h5>
                <ul>
                    <li><strong>Hohe Genauigkeit:</strong> {{ "%.1f"|format(metrics.accuracy * 100) }}% korrekte Vorhersagen</li>
                    <li><strong>Ausgewogene Performance:</strong> Gute Balance zwischen Precision und Recall</li>
                    <li><strong>Medizinische Relevanz:</strong> Hohe Sensitivität für Malaria-Erkennung</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5>Empfehlungen:</h5>
                <ul>
                    <li>Modell ist bereit für klinische Tests</li>
                    <li>Kontinuierliche Überwachung der Performance</li>
                    <li>Regelmäßige Updates mit neuen Daten</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Animate progress bars on page load
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-bar-custom');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    });
</script>
{% endblock %}
