{% extends "base.html" %}

{% block title %}Algorithmus Analyse - Malaria Erkennung{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin: 15px 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-value {
        font-size: 3rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .metric-label {
        font-size: 1.2rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .progress-custom {
        height: 20px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    
    .progress-bar-custom {
        height: 100%;
        border-radius: 10px;
        transition: width 0.8s ease;
        background: linear-gradient(90deg, #27ae60, #2ecc71);
    }
    
    .confusion-matrix {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
    }
    
    .matrix-cell {
        border: 2px solid #ecf0f1;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin: 5px;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .matrix-cell.correct {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
    }
    
    .matrix-cell.incorrect {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
    }
    
    .info-badge {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin: 5px;
        display: inline-block;
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 300;
        margin: 30px 0 20px 0;
        text-align: center;
    }
    
    .nav-pills .nav-link {
        border-radius: 25px;
        margin: 0 5px;
        transition: all 0.3s ease;
    }
    
    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }

    .upload-area-analysis {
        border: 3px dashed rgba(255, 255, 255, 0.6);
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
    }

    .upload-area-analysis:hover {
        border-color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .upload-area-analysis.dragover {
        border-color: #27ae60;
        background: rgba(39, 174, 96, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Navigation -->
    <div class="text-center mb-4">
        <ul class="nav nav-pills justify-content-center">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Startseite
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('analysis') }}">
                    <i class="fas fa-chart-bar"></i> Algorithmus Analyse
                </a>
            </li>
        </ul>
    </div>

    <h2 class="section-title">
        <i class="fas fa-brain"></i> Algorithmus Performance Analyse
    </h2>
    
    <!-- Main Metrics -->
    <div class="row">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">Genauigkeit</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.accuracy * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.accuracy * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">Precision</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.precision.average * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.precision.average * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">Recall</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.recall.average * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.recall.average * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-label">F1-Score</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.f1_score.average * 100) }}%</div>
                <div class="progress-custom">
                    <div class="progress-bar-custom" style="width: {{ metrics.f1_score.average * 100 }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Metrics -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h4><i class="fas fa-microscope"></i> Klassen-spezifische Metriken</h4>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Klasse</th>
                            <th>Precision</th>
                            <th>Recall</th>
                            <th>F1-Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Parasitized</strong></td>
                            <td>{{ "%.2f"|format(metrics.precision.parasitized * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.recall.parasitized * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.f1_score.parasitized * 100) }}%</td>
                        </tr>
                        <tr>
                            <td><strong>Uninfected</strong></td>
                            <td>{{ "%.2f"|format(metrics.precision.uninfected * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.recall.uninfected * 100) }}%</td>
                            <td>{{ "%.2f"|format(metrics.f1_score.uninfected * 100) }}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="chart-container">
                <h4><i class="fas fa-heartbeat"></i> Zusätzliche Metriken</h4>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h5>Sensitivität</h5>
                            <div class="metric-value text-primary">{{ "%.2f"|format(metrics.sensitivity * 100) }}%</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5>Spezifität</h5>
                            <div class="metric-value text-success">{{ "%.2f"|format(metrics.specificity * 100) }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confusion Matrix -->
    <div class="confusion-matrix">
        <h4 class="text-center mb-4"><i class="fas fa-table"></i> Konfusionsmatrix</h4>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th rowspan="2" class="align-middle text-center">Tatsächlich</th>
                            <th colspan="2" class="text-center">Vorhergesagt</th>
                        </tr>
                        <tr>
                            <th class="text-center">Parasitized</th>
                            <th class="text-center">Uninfected</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th>Parasitized</th>
                            <td class="matrix-cell correct">{{ metrics.confusion_matrix.true_positive }}</td>
                            <td class="matrix-cell incorrect">{{ metrics.confusion_matrix.false_negative }}</td>
                        </tr>
                        <tr>
                            <th>Uninfected</th>
                            <td class="matrix-cell incorrect">{{ metrics.confusion_matrix.false_positive }}</td>
                            <td class="matrix-cell correct">{{ metrics.confusion_matrix.true_negative }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Model Information -->
    <div class="chart-container">
        <h4><i class="fas fa-info-circle"></i> Modell Informationen</h4>
        <div class="row">
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-database"></i> Gesamt Samples: {{ metrics.total_samples }}
                </span>
            </div>
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-clock"></i> Training Zeit: {{ metrics.training_time }}
                </span>
            </div>
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-hdd"></i> Modell Größe: {{ metrics.model_size }}
                </span>
            </div>
            <div class="col-md-3">
                <span class="info-badge">
                    <i class="fas fa-calendar"></i> Letztes Update: {{ metrics.last_updated }}
                </span>
            </div>
        </div>
    </div>

    <!-- Performance Interpretation -->
    <div class="chart-container">
        <h4><i class="fas fa-lightbulb"></i> Performance Interpretation</h4>

        {% if metrics.training_note %}
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle"></i> Wichtiger Hinweis zur aktuellen Performance</h6>
            <p>{{ metrics.training_note }}</p>
        </div>
        {% endif %}

        <div class="row">
            <div class="col-md-6">
                <h5>Aktuelle Modell-Analyse:</h5>
                <ul>
                    {% if metrics.accuracy < 0.7 %}
                    <li><strong>Niedrige Genauigkeit:</strong> {{ "%.1f"|format(metrics.accuracy * 100) }}% - Modell benötigt Verbesserung</li>
                    <li><strong>Unausgewogene Klassifikation:</strong> Modell bevorzugt eine Klasse stark</li>
                    <li><strong>Sensitivität:</strong> {{ "%.1f"|format(metrics.sensitivity * 100) }}% - Malaria-Erkennung unzureichend</li>
                    {% else %}
                    <li><strong>Hohe Genauigkeit:</strong> {{ "%.1f"|format(metrics.accuracy * 100) }}% korrekte Vorhersagen</li>
                    <li><strong>Ausgewogene Performance:</strong> Gute Balance zwischen Precision und Recall</li>
                    <li><strong>Medizinische Relevanz:</strong> Hohe Sensitivität für Malaria-Erkennung</li>
                    {% endif %}
                </ul>
            </div>
            <div class="col-md-6">
                <h5>Empfehlungen zur Verbesserung:</h5>
                <ul>
                    {% if metrics.accuracy < 0.7 %}
                    <li>Mehr Trainingsepochen erforderlich</li>
                    <li>Datenaugmentation verbessern</li>
                    <li>Lernrate anpassen</li>
                    <li>Modellarchitektur überdenken</li>
                    <li>Class Balancing implementieren</li>
                    {% else %}
                    <li>Modell ist bereit für klinische Tests</li>
                    <li>Kontinuierliche Überwachung der Performance</li>
                    <li>Regelmäßige Updates mit neuen Daten</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <!-- Test Your Own Image Section -->
    <div class="chart-container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <div class="text-center mb-4">
            <h3><i class="fas fa-upload"></i> Testen Sie Ihren eigenen Fall!</h3>
            <p class="lead">Nach dem Betrachten unserer Algorithmus-Performance können Sie jetzt Ihr eigenes Blutzellen-Bild hochladen</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <form method="POST" action="{{ url_for('upload_file') }}" enctype="multipart/form-data" id="test-upload-form">
                    <div class="upload-area-analysis" id="upload-area-analysis">
                        <div class="upload-icon" style="color: white;">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <h4 style="color: white;">Ihr Blutzellen-Bild hochladen</h4>
                        <p class="mb-3" style="color: rgba(255,255,255,0.9);">
                            Laden Sie ein mikroskopisches Bild einer Blutzelle hoch, um zu testen, ob Malaria-Parasiten vorhanden sind
                        </p>
                        <p class="small" style="color: rgba(255,255,255,0.8);">
                            Unterstützte Formate: PNG, JPG, JPEG, GIF, BMP, TIFF<br>
                            Maximale Dateigröße: 16MB
                        </p>

                        <input type="file"
                               name="file"
                               id="test-file-input"
                               accept="image/*"
                               style="display: none;"
                               required>

                        <div class="mt-3">
                            <button type="button" class="btn btn-light btn-lg" onclick="document.getElementById('test-file-input').click()">
                                <i class="fas fa-folder-open"></i> Bild auswählen
                            </button>
                        </div>
                    </div>

                    <!-- File Preview for Analysis Page -->
                    <div id="test-file-preview" style="display: none;">
                        <div class="card mt-4" style="background: rgba(255,255,255,0.95); color: #333;">
                            <div class="card-body text-center">
                                <h6>Ausgewählte Datei:</h6>
                                <p id="test-file-name" class="text-muted"></p>
                                <div id="test-image-preview" class="mt-3"></div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-search"></i> Malaria-Test starten
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetTestForm()">
                                        <i class="fas fa-times"></i> Zurücksetzen
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Loading Animation for Test -->
                <div class="loading" id="test-loading" style="display: none;">
                    <div class="spinner" style="border-top-color: white;"></div>
                    <h5 style="color: white;">Analysiere Ihr Bild...</h5>
                    <p style="color: rgba(255,255,255,0.8);">Unser Algorithmus überprüft auf Malaria-Parasiten</p>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="text-center">
                    <h5><i class="fas fa-shield-alt"></i> Vertrauen Sie unserem Algorithmus</h5>
                    <p>Mit {{ "%.1f"|format(metrics.accuracy * 100) }}% Genauigkeit und {{ "%.1f"|format(metrics.sensitivity * 100) }}% Sensitivität</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <h5><i class="fas fa-clock"></i> Schnelle Ergebnisse</h5>
                    <p>Erhalten Sie Ihre Diagnose in wenigen Sekunden</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Animate progress bars on page load
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-bar-custom');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });

        // Setup test upload functionality
        setupTestUpload();
    });

    function setupTestUpload() {
        const testFileInput = document.getElementById('test-file-input');
        const testUploadArea = document.getElementById('upload-area-analysis');

        if (!testFileInput || !testUploadArea) return;

        // File input change event
        testFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                showTestFilePreview(file);
            }
        });

        // Click to upload
        testUploadArea.addEventListener('click', function() {
            testFileInput.click();
        });

        // Drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            testUploadArea.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            testUploadArea.addEventListener(eventName, highlightTest, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            testUploadArea.addEventListener(eventName, unhighlightTest, false);
        });

        testUploadArea.addEventListener('drop', handleTestDrop, false);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlightTest(e) {
            testUploadArea.classList.add('dragover');
        }

        function unhighlightTest(e) {
            testUploadArea.classList.remove('dragover');
        }

        function handleTestDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                testFileInput.files = files;
                showTestFilePreview(files[0]);
            }
        }
    }

    function showTestFilePreview(file) {
        const preview = document.getElementById('test-file-preview');
        const fileName = document.getElementById('test-file-name');
        const imagePreview = document.getElementById('test-image-preview');

        fileName.textContent = file.name;

        // Show image preview
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.innerHTML = `<img src="${e.target.result}" class="preview-image" alt="Test Preview" style="max-height: 200px;">`;
        };
        reader.readAsDataURL(file);

        preview.style.display = 'block';

        // Scroll to preview
        preview.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    function resetTestForm() {
        document.getElementById('test-upload-form').reset();
        document.getElementById('test-file-preview').style.display = 'none';
        document.getElementById('test-image-preview').innerHTML = '';
    }

    // Show loading animation on test form submit
    document.getElementById('test-upload-form').addEventListener('submit', function() {
        document.getElementById('test-loading').style.display = 'block';
        document.getElementById('test-file-preview').style.opacity = '0.7';
    });
</script>
{% endblock %}
