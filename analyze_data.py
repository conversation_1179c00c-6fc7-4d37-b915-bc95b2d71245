#!/usr/bin/env python3
"""
Comprehensive Data Analysis for Malaria Detection Dataset
Analyzes all available data to understand dataset characteristics and quality
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import cv2
from collections import defaultdict
import json
from datetime import datetime

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class MalariaDataAnalyzer:
    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.analysis_results = {}
        self.image_stats = defaultdict(list)
        
    def analyze_dataset_structure(self):
        """Analyze the overall structure of the dataset"""
        print("🔍 Analyzing Dataset Structure...")
        
        structure = {}
        for root, dirs, files in os.walk(self.data_dir):
            rel_path = os.path.relpath(root, self.data_dir)
            if rel_path == '.':
                rel_path = 'root'
            
            image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            structure[rel_path] = {
                'directories': dirs,
                'image_count': len(image_files),
                'total_files': len(files)
            }
            
        self.analysis_results['structure'] = structure
        
        # Print structure
        for path, info in structure.items():
            print(f"📁 {path}: {info['image_count']} images, {info['total_files']} total files")
            
        return structure
    
    def analyze_class_distribution(self):
        """Analyze the distribution of classes"""
        print("\n📊 Analyzing Class Distribution...")
        
        class_counts = {}
        
        # Check train directory
        train_dir = os.path.join(self.data_dir, 'train')
        if os.path.exists(train_dir):
            for class_name in os.listdir(train_dir):
                class_path = os.path.join(train_dir, class_name)
                if os.path.isdir(class_path):
                    image_files = [f for f in os.listdir(class_path) 
                                 if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                    class_counts[f'train_{class_name}'] = len(image_files)
        
        # Check test directory
        test_dir = os.path.join(self.data_dir, 'test')
        if os.path.exists(test_dir):
            for class_name in os.listdir(test_dir):
                class_path = os.path.join(test_dir, class_name)
                if os.path.isdir(class_path):
                    image_files = [f for f in os.listdir(class_path) 
                                 if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                    class_counts[f'test_{class_name}'] = len(image_files)
        
        self.analysis_results['class_distribution'] = class_counts
        
        # Print distribution
        total_images = sum(class_counts.values())
        print(f"📈 Total Images: {total_images}")
        for class_name, count in class_counts.items():
            percentage = (count / total_images) * 100 if total_images > 0 else 0
            print(f"   {class_name}: {count} images ({percentage:.1f}%)")
            
        return class_counts
    
    def analyze_image_properties(self, sample_size=1000):
        """Analyze image properties like size, format, quality"""
        print(f"\n🖼️  Analyzing Image Properties (sampling {sample_size} images)...")
        
        image_data = []
        sample_count = 0
        
        for root, dirs, files in os.walk(self.data_dir):
            if sample_count >= sample_size:
                break
                
            image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            
            for img_file in image_files:
                if sample_count >= sample_size:
                    break
                    
                img_path = os.path.join(root, img_file)
                try:
                    # Get basic info
                    with Image.open(img_path) as img:
                        width, height = img.size
                        format_type = img.format
                        mode = img.mode
                        
                    # Get file size
                    file_size = os.path.getsize(img_path)
                    
                    # Get class from path
                    path_parts = os.path.normpath(root).split(os.sep)
                    class_name = path_parts[-1] if len(path_parts) > 1 else 'unknown'
                    split_type = path_parts[-2] if len(path_parts) > 2 else 'unknown'
                    
                    image_data.append({
                        'path': img_path,
                        'width': width,
                        'height': height,
                        'format': format_type,
                        'mode': mode,
                        'file_size_kb': file_size / 1024,
                        'aspect_ratio': width / height,
                        'total_pixels': width * height,
                        'class': class_name,
                        'split': split_type
                    })
                    
                    sample_count += 1
                    
                except Exception as e:
                    print(f"⚠️  Error processing {img_path}: {e}")
                    continue
        
        df = pd.DataFrame(image_data)
        self.analysis_results['image_properties'] = df
        
        # Print summary statistics
        print(f"📊 Analyzed {len(df)} images")
        print(f"   Average size: {df['width'].mean():.0f} x {df['height'].mean():.0f} pixels")
        print(f"   Size range: {df['width'].min()}-{df['width'].max()} x {df['height'].min()}-{df['height'].max()}")
        print(f"   Average file size: {df['file_size_kb'].mean():.1f} KB")
        print(f"   Formats: {df['format'].value_counts().to_dict()}")
        print(f"   Color modes: {df['mode'].value_counts().to_dict()}")
        
        return df
    
    def check_data_quality(self, sample_size=500):
        """Check for data quality issues"""
        print(f"\n🔍 Checking Data Quality (sampling {sample_size} images)...")
        
        quality_issues = {
            'corrupted_images': [],
            'very_small_images': [],
            'very_large_images': [],
            'unusual_aspect_ratios': [],
            'grayscale_images': [],
            'low_quality_images': []
        }
        
        sample_count = 0
        
        for root, dirs, files in os.walk(self.data_dir):
            if sample_count >= sample_size:
                break
                
            image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            
            for img_file in image_files:
                if sample_count >= sample_size:
                    break
                    
                img_path = os.path.join(root, img_file)
                try:
                    with Image.open(img_path) as img:
                        width, height = img.size
                        mode = img.mode
                        
                        # Check for very small images
                        if width < 50 or height < 50:
                            quality_issues['very_small_images'].append(img_path)
                        
                        # Check for very large images
                        if width > 2000 or height > 2000:
                            quality_issues['very_large_images'].append(img_path)
                        
                        # Check aspect ratio
                        aspect_ratio = width / height
                        if aspect_ratio < 0.5 or aspect_ratio > 2.0:
                            quality_issues['unusual_aspect_ratios'].append(img_path)
                        
                        # Check if grayscale
                        if mode in ['L', 'LA']:
                            quality_issues['grayscale_images'].append(img_path)
                        
                        # Check file size for quality estimation
                        file_size = os.path.getsize(img_path)
                        pixels = width * height
                        if file_size < pixels * 0.1:  # Very compressed
                            quality_issues['low_quality_images'].append(img_path)
                    
                    sample_count += 1
                    
                except Exception as e:
                    quality_issues['corrupted_images'].append(img_path)
                    sample_count += 1
        
        self.analysis_results['quality_issues'] = quality_issues
        
        # Print quality report
        print("📋 Data Quality Report:")
        for issue_type, files in quality_issues.items():
            if files:
                print(f"   ⚠️  {issue_type}: {len(files)} files")
            else:
                print(f"   ✅ {issue_type}: No issues found")
        
        return quality_issues
    
    def generate_visualizations(self):
        """Generate visualization plots"""
        print("\n📈 Generating Visualizations...")
        
        # Create output directory
        os.makedirs('analysis_output', exist_ok=True)
        
        # Class distribution plot
        if 'class_distribution' in self.analysis_results:
            plt.figure(figsize=(12, 6))
            class_data = self.analysis_results['class_distribution']
            
            # Separate train and test data
            train_data = {k.replace('train_', ''): v for k, v in class_data.items() if k.startswith('train_')}
            test_data = {k.replace('test_', ''): v for k, v in class_data.items() if k.startswith('test_')}
            
            x = np.arange(len(train_data))
            width = 0.35
            
            plt.bar(x - width/2, list(train_data.values()), width, label='Training', alpha=0.8)
            plt.bar(x + width/2, list(test_data.values()), width, label='Testing', alpha=0.8)
            
            plt.xlabel('Classes')
            plt.ylabel('Number of Images')
            plt.title('Class Distribution: Training vs Testing Data')
            plt.xticks(x, list(train_data.keys()))
            plt.legend()
            plt.tight_layout()
            plt.savefig('analysis_output/class_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # Image properties plots
        if 'image_properties' in self.analysis_results:
            df = self.analysis_results['image_properties']
            
            # Size distribution
            plt.figure(figsize=(15, 10))
            
            plt.subplot(2, 3, 1)
            plt.hist(df['width'], bins=30, alpha=0.7, edgecolor='black')
            plt.xlabel('Width (pixels)')
            plt.ylabel('Frequency')
            plt.title('Image Width Distribution')
            
            plt.subplot(2, 3, 2)
            plt.hist(df['height'], bins=30, alpha=0.7, edgecolor='black')
            plt.xlabel('Height (pixels)')
            plt.ylabel('Frequency')
            plt.title('Image Height Distribution')
            
            plt.subplot(2, 3, 3)
            plt.hist(df['file_size_kb'], bins=30, alpha=0.7, edgecolor='black')
            plt.xlabel('File Size (KB)')
            plt.ylabel('Frequency')
            plt.title('File Size Distribution')
            
            plt.subplot(2, 3, 4)
            plt.scatter(df['width'], df['height'], alpha=0.5)
            plt.xlabel('Width (pixels)')
            plt.ylabel('Height (pixels)')
            plt.title('Width vs Height')
            
            plt.subplot(2, 3, 5)
            plt.hist(df['aspect_ratio'], bins=30, alpha=0.7, edgecolor='black')
            plt.xlabel('Aspect Ratio')
            plt.ylabel('Frequency')
            plt.title('Aspect Ratio Distribution')
            
            plt.subplot(2, 3, 6)
            class_counts = df['class'].value_counts()
            plt.pie(class_counts.values, labels=class_counts.index, autopct='%1.1f%%')
            plt.title('Class Distribution in Sample')
            
            plt.tight_layout()
            plt.savefig('analysis_output/image_properties.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        print("📊 Visualizations saved to 'analysis_output/' directory")
    
    def save_analysis_report(self):
        """Save comprehensive analysis report"""
        print("\n💾 Saving Analysis Report...")
        
        # Create output directory
        os.makedirs('analysis_output', exist_ok=True)
        
        # Save detailed JSON report
        report_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'dataset_structure': self.analysis_results.get('structure', {}),
            'class_distribution': self.analysis_results.get('class_distribution', {}),
            'quality_issues': self.analysis_results.get('quality_issues', {}),
        }
        
        # Add image properties summary if available
        if 'image_properties' in self.analysis_results:
            df = self.analysis_results['image_properties']
            report_data['image_properties_summary'] = {
                'total_analyzed': len(df),
                'avg_width': float(df['width'].mean()),
                'avg_height': float(df['height'].mean()),
                'avg_file_size_kb': float(df['file_size_kb'].mean()),
                'format_distribution': df['format'].value_counts().to_dict(),
                'mode_distribution': df['mode'].value_counts().to_dict(),
                'size_statistics': {
                    'width': {'min': int(df['width'].min()), 'max': int(df['width'].max()), 'std': float(df['width'].std())},
                    'height': {'min': int(df['height'].min()), 'max': int(df['height'].max()), 'std': float(df['height'].std())},
                    'file_size_kb': {'min': float(df['file_size_kb'].min()), 'max': float(df['file_size_kb'].max()), 'std': float(df['file_size_kb'].std())}
                }
            }
        
        with open('analysis_output/data_analysis_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        # Create human-readable report
        with open('analysis_output/data_analysis_report.txt', 'w') as f:
            f.write("MALARIA DETECTION DATASET ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Dataset structure
            f.write("DATASET STRUCTURE:\n")
            f.write("-" * 20 + "\n")
            for path, info in report_data['dataset_structure'].items():
                f.write(f"{path}: {info['image_count']} images\n")
            f.write("\n")
            
            # Class distribution
            f.write("CLASS DISTRIBUTION:\n")
            f.write("-" * 20 + "\n")
            total_images = sum(report_data['class_distribution'].values())
            for class_name, count in report_data['class_distribution'].items():
                percentage = (count / total_images) * 100 if total_images > 0 else 0
                f.write(f"{class_name}: {count} images ({percentage:.1f}%)\n")
            f.write(f"Total: {total_images} images\n\n")
            
            # Quality issues
            f.write("DATA QUALITY ISSUES:\n")
            f.write("-" * 20 + "\n")
            for issue_type, files in report_data['quality_issues'].items():
                f.write(f"{issue_type}: {len(files)} files\n")
            f.write("\n")
            
            # Image properties summary
            if 'image_properties_summary' in report_data:
                props = report_data['image_properties_summary']
                f.write("IMAGE PROPERTIES SUMMARY:\n")
                f.write("-" * 25 + "\n")
                f.write(f"Analyzed: {props['total_analyzed']} images\n")
                f.write(f"Average size: {props['avg_width']:.0f} x {props['avg_height']:.0f} pixels\n")
                f.write(f"Average file size: {props['avg_file_size_kb']:.1f} KB\n")
                f.write(f"Formats: {props['format_distribution']}\n")
                f.write(f"Color modes: {props['mode_distribution']}\n")
        
        print("📄 Analysis report saved to 'analysis_output/data_analysis_report.txt'")
        print("📊 Detailed data saved to 'analysis_output/data_analysis_report.json'")

def main():
    """Main analysis function"""
    print("🔬 MALARIA DETECTION DATASET ANALYSIS")
    print("=" * 50)
    
    analyzer = MalariaDataAnalyzer()
    
    # Run all analyses
    analyzer.analyze_dataset_structure()
    analyzer.analyze_class_distribution()
    analyzer.analyze_image_properties(sample_size=2000)
    analyzer.check_data_quality(sample_size=1000)
    analyzer.generate_visualizations()
    analyzer.save_analysis_report()
    
    print("\n✅ Analysis Complete!")
    print("📁 Check 'analysis_output/' directory for detailed results")

if __name__ == "__main__":
    main()
