{"cells": [{"cell_type": "code", "metadata": {"_cell_guid": "79c7e3d0-c299-4dcb-8224-4455121ee9b0", "_uuid": "d629ff2d2480ee46fbb7e2d37f6b5fab8052498a", "jupyter": {"is_executing": true}}, "source": ["from keras.models import Sequential\n", "from keras.layers import Convolution2D\n", "from keras.layers import MaxPooling2D\n", "from keras.layers import Flatten\n", "from keras.layers import Dense\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:40.010675Z", "start_time": "2024-12-09T18:17:39.840929Z"}}, "source": ["# Initialising the CNN\n", "classifier = Sequential()"], "outputs": [{"ename": "NameError", "evalue": "name 'Sequential' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Initialising the CNN\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m classifier \u001b[38;5;241m=\u001b[39m \u001b[43mSequential\u001b[49m()\n", "\u001b[1;31mNameError\u001b[0m: name 'Sequential' is not defined"]}], "execution_count": 3}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:41.711764Z", "start_time": "2024-12-09T18:17:41.679543Z"}}, "source": ["# Step1 - Convolution\n", "classifier.add(Convolution2D(64, 3, 3, input_shape = (64, 64, 3), activation = 'relu'))"], "outputs": [{"ename": "NameError", "evalue": "name 'classifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Step1 - Convolution\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mclassifier\u001b[49m\u001b[38;5;241m.\u001b[39madd(Convolution2D(\u001b[38;5;241m64\u001b[39m, \u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m3\u001b[39m, input_shape \u001b[38;5;241m=\u001b[39m (\u001b[38;5;241m64\u001b[39m, \u001b[38;5;241m64\u001b[39m, \u001b[38;5;241m3\u001b[39m), activation \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrelu\u001b[39m\u001b[38;5;124m'\u001b[39m))\n", "\u001b[1;31mNameError\u001b[0m: name 'classifier' is not defined"]}], "execution_count": 4}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:42.491900Z", "start_time": "2024-12-09T18:17:42.465949Z"}}, "source": ["# Step2 - Pooling\n", "classifier.add(MaxPooling2D(pool_size = (2,2)))"], "outputs": [{"ename": "NameError", "evalue": "name 'classifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Step2 - Pooling\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mclassifier\u001b[49m\u001b[38;5;241m.\u001b[39madd(MaxPooling2D(pool_size \u001b[38;5;241m=\u001b[39m (\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m2\u001b[39m)))\n", "\u001b[1;31mNameError\u001b[0m: name 'classifier' is not defined"]}], "execution_count": 5}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:43.253872Z", "start_time": "2024-12-09T18:17:43.227493Z"}}, "source": ["# Adding a second convolution layer\n", "classifier.add(Convolution2D(64, 3, 3, activation = 'relu'))\n", "classifier.add(MaxPooling2D(pool_size = (2,2)))"], "outputs": [{"ename": "NameError", "evalue": "name 'classifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Adding a second convolution layer\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mclassifier\u001b[49m\u001b[38;5;241m.\u001b[39madd(Convolution2D(\u001b[38;5;241m64\u001b[39m, \u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m3\u001b[39m, activation \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrelu\u001b[39m\u001b[38;5;124m'\u001b[39m))\n\u001b[0;32m      3\u001b[0m classifier\u001b[38;5;241m.\u001b[39madd(MaxPooling2D(pool_size \u001b[38;5;241m=\u001b[39m (\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m2\u001b[39m)))\n", "\u001b[1;31mNameError\u001b[0m: name 'classifier' is not defined"]}], "execution_count": 6}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:43.959848Z", "start_time": "2024-12-09T18:17:43.934801Z"}}, "source": ["# step3 - Flattening\n", "classifier.add(<PERSON>ten())"], "outputs": [{"ename": "NameError", "evalue": "name 'classifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# step3 - Flattening\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mclassifier\u001b[49m\u001b[38;5;241m.\u001b[39madd(<PERSON>ten())\n", "\u001b[1;31mNameError\u001b[0m: name 'classifier' is not defined"]}], "execution_count": 7}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:44.705543Z", "start_time": "2024-12-09T18:17:44.675489Z"}}, "source": ["# Step4 - Full COnnection\n", "classifier.add(Dense(output_dim = 128, activation = 'relu'))\n", "classifier.add(Dense(output_dim = 1, activation = 'sigmoid'))"], "outputs": [{"ename": "NameError", "evalue": "name 'classifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Step4 - Full COnnection\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mclassifier\u001b[49m\u001b[38;5;241m.\u001b[39madd(Dense(output_dim \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m128\u001b[39m, activation \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrelu\u001b[39m\u001b[38;5;124m'\u001b[39m))\n\u001b[0;32m      3\u001b[0m classifier\u001b[38;5;241m.\u001b[39madd(Dense(output_dim \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m, activation \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msigmoid\u001b[39m\u001b[38;5;124m'\u001b[39m))\n", "\u001b[1;31mNameError\u001b[0m: name 'classifier' is not defined"]}], "execution_count": 8}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:45.732990Z", "start_time": "2024-12-09T18:17:45.693696Z"}}, "source": ["# Compiling the CNN\n", "classifier.compile(optimizer ='adam', loss='binary_crossentropy', metrics=['accuracy'])"], "outputs": [{"ename": "NameError", "evalue": "name 'classifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Compiling the CNN\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mclassifier\u001b[49m\u001b[38;5;241m.\u001b[39mcompile(optimizer \u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124madam\u001b[39m\u001b[38;5;124m'\u001b[39m, loss\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbinary_crossentropy\u001b[39m\u001b[38;5;124m'\u001b[39m, metrics\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124maccuracy\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[1;31mNameError\u001b[0m: name 'classifier' is not defined"]}], "execution_count": 9}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:47.530189Z", "start_time": "2024-12-09T18:17:46.533716Z"}}, "source": ["from keras.preprocessing.image import ImageDataGenerator\n", "\n", "train_datagen = ImageDataGenerator(rescale=1./255,\n", "                                   shear_range=0.2,\n", "                                   zoom_range=0.2,\n", "                                   horizontal_flip=True)\n", "\n", "test_datagen = ImageDataGenerator(rescale=1./255)"], "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'tensorflow'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpreprocessing\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimage\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ImageDataGenerator\n\u001b[0;32m      3\u001b[0m train_datagen \u001b[38;5;241m=\u001b[39m ImageDataGenerator(rescale\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1.\u001b[39m\u001b[38;5;241m/\u001b[39m\u001b[38;5;241m255\u001b[39m,\n\u001b[0;32m      4\u001b[0m                                    shear_range\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.2\u001b[39m,\n\u001b[0;32m      5\u001b[0m                                    zoom_range\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.2\u001b[39m,\n\u001b[0;32m      6\u001b[0m                                    horizontal_flip\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m      8\u001b[0m test_datagen \u001b[38;5;241m=\u001b[39m ImageDataGenerator(rescale\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1.\u001b[39m\u001b[38;5;241m/\u001b[39m\u001b[38;5;241m255\u001b[39m)\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\__init__.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# DO NOT EDIT. Generated by api_gen.sh\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DTypePolicy\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FloatDTypePolicy\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01ma<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Function\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\api\\__init__.py:8\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;124;03m\"\"\"DO NOT EDIT.\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \n\u001b[0;32m      3\u001b[0m \u001b[38;5;124;03mThis file was autogenerated. Do not edit it by hand,\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;124;03msince your modifications would be overwritten.\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m----> 8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m activations\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON>as\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m applications\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m backend\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\api\\activations\\__init__.py:7\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;124;03m\"\"\"DO NOT EDIT.\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \n\u001b[0;32m      3\u001b[0m \u001b[38;5;124;03mThis file was autogenerated. Do not edit it by hand,\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;124;03msince your modifications would be overwritten.\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m----> 7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m deserialize\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m serialize\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\__init__.py:1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m activations\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m applications\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m backend\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\activations\\__init__.py:3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtypes\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m celu\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m elu\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mactivations\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m exponential\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\activations\\activations.py:1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m backend\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi_export\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m keras_export\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\backend\\__init__.py:10\u001b[0m\n\u001b[0;32m      7\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi_export\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m keras_export\n\u001b[1;32m---> 10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mker<PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m result_type\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mkeras_tensor\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m KerasTensor\n\u001b[0;32m     12\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mkeras_tensor\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m any_symbolic_tensors\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\backend\\common\\__init__.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommon\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m backend_utils\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m result_type\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvariables\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutocastScope\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvariables\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Variable \u001b[38;5;28;01mas\u001b[39;00m KerasVariable\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\backend\\common\\dtypes.py:5\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi_export\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m keras_export\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m config\n\u001b[1;32m----> 5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvariables\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m standardize_dtype\n\u001b[0;32m      7\u001b[0m BOOL_TYPES \u001b[38;5;241m=\u001b[39m (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbool\u001b[39m\u001b[38;5;124m\"\u001b[39m,)\n\u001b[0;32m      8\u001b[0m INT_TYPES \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m      9\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muint8\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     10\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muint16\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     16\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mint64\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     17\u001b[0m )\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\backend\\common\\variables.py:12\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON>mon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstateless_scope\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m in_stateless_scope\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodule_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m tensorflow \u001b[38;5;28;01mas\u001b[39;00m tf\n\u001b[1;32m---> 12\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnaming\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m auto_name\n\u001b[0;32m     15\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mVariable\u001b[39;00m:\n\u001b[0;32m     16\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Represents a backend-agnostic variable in Keras.\u001b[39;00m\n\u001b[0;32m     17\u001b[0m \n\u001b[0;32m     18\u001b[0m \u001b[38;5;124;03m    A `Variable` acts as a container for state. It holds a tensor value and can\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     86\u001b[0m \u001b[38;5;124;03m    ```\u001b[39;00m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\utils\\__init__.py:1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01maudio_dataset_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m audio_dataset_from_directory\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdataset_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m split_dataset\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfile_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_file\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\utils\\audio_dataset_utils.py:4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi_export\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m keras_export\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m dataset_utils\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodule_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m tensorflow \u001b[38;5;28;01mas\u001b[39;00m tf\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodule_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m tensorflow_io \u001b[38;5;28;01mas\u001b[39;00m tfio\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\utils\\dataset_utils.py:9\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmultiprocessing\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpool\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ThreadPool\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m----> 9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m tree\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi_export\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m keras_export\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m io_utils\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\tree\\__init__.py:1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree_api\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m assert_same_paths\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree_api\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m assert_same_structure\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree_api\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m flatten\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\tree\\tree_api.py:6\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodule_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m optree\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m optree\u001b[38;5;241m.\u001b[39mavailable:\n\u001b[1;32m----> 6\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m optree_impl \u001b[38;5;28;01mas\u001b[39;00m tree_impl\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m dmtree\u001b[38;5;241m.\u001b[39mavailable:\n\u001b[0;32m      8\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mkeras\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msrc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m dmtree_impl \u001b[38;5;28;01mas\u001b[39;00m tree_impl\n", "File \u001b[1;32m~\\Downloads\\DM_Muhamad_Alshbib_3A\\amazon\\venv\\Lib\\site-packages\\keras\\src\\tree\\optree_impl.py:17\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;66;03m# Register backend-specific node classes\u001b[39;00m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m backend() \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtensorflow\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m---> 17\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpython\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtrackable\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdata_structures\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ListWrapper\n\u001b[0;32m     19\u001b[0m     optree\u001b[38;5;241m.\u001b[39mregister_pytree_node(\n\u001b[0;32m     20\u001b[0m         ListWrapper,\n\u001b[0;32m     21\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x: (x, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[0;32m     22\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m metadata, children: ListWrapper(\u001b[38;5;28mlist\u001b[39m(children)),\n\u001b[0;32m     23\u001b[0m         namespace\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mkeras\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     24\u001b[0m     )\n\u001b[0;32m     26\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtensorflow\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpython\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtrackable\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdata_structures\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _DictWrapper\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'tensorflow'"]}], "execution_count": 10}, {"cell_type": "code", "metadata": {}, "source": ["training_set = train_datagen.flow_from_directory(\n", "        '/kaggle/input/files1/Malaria Cells/training_set',\n", "        target_size=(64, 64),\n", "        batch_size=32,\n", "        class_mode='binary')\n", "\n", "test_set = test_datagen.flow_from_directory(\n", "        '/kaggle/input/files1/Malaria Cells/testing_set',\n", "        target_size=(64, 64),\n", "        batch_size=32,\n", "        class_mode='binary')"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {}, "source": ["### Early Stopping"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:57.919299Z", "start_time": "2024-12-09T18:17:57.886329Z"}}, "source": ["from tensorflow.keras.callbacks import EarlyStopping"], "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'tensorflow'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcallbacks\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m EarlyStopping\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'tensorflow'"]}], "execution_count": 11}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-09T18:17:59.158217Z", "start_time": "2024-12-09T18:17:59.134181Z"}}, "source": ["early_stop = EarlyStopping(monitor='val_loss',patience=2)"], "outputs": [{"ename": "NameError", "evalue": "name 'EarlyStopping' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m early_stop \u001b[38;5;241m=\u001b[39m \u001b[43mEarlyStopping\u001b[49m(monitor\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mval_loss\u001b[39m\u001b[38;5;124m'\u001b[39m,patience\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'EarlyStopping' is not defined"]}], "execution_count": 12}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10\n", "5000/5000 [==============================] - 396s 79ms/step - loss: 0.0995 - accuracy: 0.9653 - val_loss: 0.0287 - val_accuracy: 0.9701\n", "Epoch 5/10\n", "2474/5000 [=============>................] - ETA: 3:00 - loss: 0.0923 - accuracy: 0.9679"]}], "source": ["# nb_epochs how much times you want to back propogate\n", "# steps_per_epoch it will transfer that many images at 1 time\n", "# & epochs means 'steps_per_epoch' will repeat that many times\n", "classifier.fit_generator(\n", "        training_set,\n", "        steps_per_epoch=5000,\n", "        nb_epoch=10,\n", "        validation_data=test_set,\n", "        nb_val_samples=1000,\n", "        callbacks=[early_stop])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluating the Model"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "losses = pd.DataFrame(classifier.history.history)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7ff0104035c0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYQAAAD8CAYAAAB3u9PLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzt3Xl4VOX1wPHvyb6vJGwBEvY9oYTFpbgviAIKSHC3VqrWvVq17qitdhG15WerVcS6AAIKCopaRcQiECAQdiIECGsIISRA9vf3x52ESQxkAknuTOZ8nmeeTO59750zEefMu4sxBqWUUsrH7gCUUkq5B00ISimlAE0ISimlHDQhKKWUAjQhKKWUctCEoJRSCtCEoJRSykETglJKKUATglJKKQc/uwNoiFatWpnExES7w1BKKY+ycuXKg8aYuPrKeVRCSExMJD093e4wlFLKo4jIDlfKaZORUkopQBOCUkopB00ISimlAA/rQ1BKeZ+ysjJycnIoLi62OxS3FxQUREJCAv7+/qd1vSYEpZRby8nJITw8nMTERETE7nDcljGGvLw8cnJySEpKOq17aJORUsqtFRcXExsbq8mgHiJCbGzsGdWkNCEopdyeJgPXnOnfyaMSwrHScrtDUEqpFsujEsLOQ8c5UlxmdxhKKS8TFhZmdwjNwqMSQllFJc/MXW93GEop1SJ5VEKIDw9kzurdzM3YbXcoSikvZIzh4Ycfpm/fvvTr148ZM2YAsHfvXoYNG0ZKSgp9+/bl+++/p6KigltuuaW67OTJk22Ovn4eNew0PiKITh2jeOKTdQzsFE1CdIjdISmlmtGzn65nw54jjXrP3u0iePqqPi6VnTNnDhkZGaxZs4aDBw8yaNAghg0bxgcffMBll13G448/TkVFBceOHSMjI4Pdu3ezbt06AA4fPtyocTcFj6ohCPDK+AEYAw/OXENFpbE7JKWUF1myZAkTJkzA19eX1q1bc95557FixQoGDRrE1KlTeeaZZ8jMzCQ8PJzOnTuzbds27rnnHr744gsiIiLsDr9eHlVDAOgYG8KzI/vwu4/W8M/vfuK3F3S1OySlVDNx9Zt8UzGm7i+hw4YNY/HixcyfP58bb7yRhx9+mJtuuok1a9awcOFCpkyZwsyZM3n77bebOeKG8agaQpVrftGeEf3bMvmrLazNcf9qmFKqZRg2bBgzZsygoqKC3NxcFi9ezODBg9mxYwfx8fHcfvvt3HbbbaxatYqDBw9SWVnJmDFjeO6551i1apXd4dfL42oIYE2++OPofqzakc/90zP47N5zCQnwyLeilPIgV199NUuXLiU5ORkR4c9//jNt2rRh2rRp/OUvf8Hf35+wsDDeffdddu/eza233kplZSUAf/rTn2yOvn5ysiqQO0pNTTXOG+Qs/SmP6/79I2mDOvKna/rZGJlSqqls3LiRXr162R2Gx6jr7yUiK40xqfVd61lNRpU1Zyqf1SWW3wzrwofLd7Jw/T6bglJKqZbBsxLC4Z1Qq0bz4CXd6ds+gkdnr2X/EV0eVymlTpdLCUFELheRzSKSJSKP1nH+QRHZICJrReS/ItLJ6dzNIrLV8bjZ6fhAEcl03PM1cWVVpuIC+PH1GocC/Hx4ZfwAjpdV8NBHa6jUoahKKXVa6k0IIuILTAGGA72BCSLSu1ax1UCqMaY/MAv4s+PaGOBpYAgwGHhaRKId17wOTAS6OR6X1xttUBR89RTsWV3jcNf4MJ68sjffbz3I1P9l13sbpZRSP+dKDWEwkGWM2WaMKQWmA6OcCxhjvjXGHHP8+iOQ4Hh+GfCVMeaQMSYf+Aq4XETaAhHGmKXG6tV+FxhdbyRRHSAsHmb9CkoKa5y6bnBHLu7Vmpc+38TGvY07k1EppbyBKwmhPbDL6fccx7GTuQ34vJ5r2zue13tPEZkoIukikp6blw/XvAn52TD/odrleGlMPyKC/bl/egbFZRUuvDWllFJVXEkIdbXt19lQLyI3AKnAX+q51uV7GmPeMMakGmNS4+LiIPEcOO8RWDsdMj6sUTY2LJC/juvP5v2FvPTFppO9H9VSHD8M718LW7+yOxKlWgRXEkIO0MHp9wRgT+1CInIx8Dgw0hhTUs+1OZxoVjrpPU9q2MPQ6VyY/zs4mFXj1Pk94rnl7ESm/pDNos0HXL6l8jCVlfDJnbB1IXz3Z7ujUaqGU+2fkJ2dTd++fZsxGte5khBWAN1EJElEAoA0YJ5zAREZAPwLKxk4fwovBC4VkWhHZ/KlwEJjzF6gUESGOkYX3QTMdT1qX7jmDfALgFm3QnlJjdOPDu9Jj9bhPPTRWvKKSk5yE+XRfpgMmxdA2xTIWQ55P9kdkVIer971Howx5SJyN9aHuy/wtjFmvYhMAtKNMfOwmojCgI8co0d3GmNGGmMOichzWEkFYJIx5pDj+Z3AO0AwVp/D5zREZHsY/Tp8mAZfPQ3DX6w+FeTvyytpKYz6xw88MjuTN28aqHuytiTbFsE3z0Ofa+CyF2ByH1gzHS583O7IVFP7/FHYl9m492zTr8bnR10eeeQROnXqxF133QXAM888g4iwePFi8vPzKSsr4/nnn2fUqFGnvE9txcXF3HnnnaSnp+Pn58fLL7/MBRdcwPr167n11lspLS2lsrKS2bNn065dO6699lpycnKoqKjgySefZPz48af9tuvi0gJAxpgFwIJax55yen7xKa59G/jZEn/GmHTgzOpNPYbDkDtg2evQ+Tzrd4debSN4ZHhPnvtsAx8s38n1Qzqd4kbKYxTkWKPMWnWHkX+HwDDofL7Vp3T+Y+DjWXMtlWdIS0vj/vvvr04IM2fO5IsvvuCBBx4gIiKCgwcPMnToUEaOHNmgL59TpkwBIDMzk02bNnHppZeyZcsW/vnPf3Lfffdx/fXXU1paSkVFBQsWLKBdu3bMnz8fgIKCgkZ/n56/Itwlk2DH/+CTu+COJVbNweHWsxNZtPkAz322gSFJsXSN9459UVus8hKYebP189r/WMkAIHkCzLkddi61Bh2olqueb/JNZcCAARw4cIA9e/aQm5tLdHQ0bdu25YEHHmDx4sX4+Piwe/du9u/fT5s2bVy+75IlS7jnnnsA6NmzJ506dWLLli2cddZZvPDCC+Tk5HDNNdfQrVs3+vXrx0MPPcQjjzzClVdeyS9/+ctGf5+e/3XKLxDGTrU+JOZMhMoTw019fIS/jksm2N+X+2esprS80sZA1Rlb+AfYnQ6jpkBc9xPHe46AgDBY84F9sakWb+zYscyaNYsZM2aQlpbG+++/T25uLitXriQjI4PWrVtTXNyw5XNOtrjoddddx7x58wgODuayyy7jm2++oXv37qxcuZJ+/frx2GOPMWnSpMZ4WzV4fkIAaNUVRvwVdiyBxX+tcap1RBAvjunPut1HmPz1FpsCVGdszQxY8W84627oU2sOY0Ao9B4F6+dC6bG6r1fqDKWlpTF9+nRmzZrF2LFjKSgoID4+Hn9/f7799lt27NjR4HsOGzaM999/H4AtW7awc+dOevTowbZt2+jcuTP33nsvI0eOZO3atezZs4eQkBBuuOEGHnrooSbZX6FlJASwmg36j4fvXrSakJxc1qcNEwZ34J/f/cTSn/JsClCdtn3r4NP7oNM5cPGzdZdJngClhdbII6WaQJ8+fSgsLKR9+/a0bduW66+/nvT0dFJTU3n//ffp2bNng+951113UVFRQb9+/Rg/fjzvvPMOgYGBzJgxg759+5KSksKmTZu46aabyMzMZPDgwaSkpPDCCy/wxBNPNPp79Oj9EH6mpBD+NcxqPrpjCYTEVJ86VlrOla8t4XhZBV/cN4zIEP9miFidseOH4c0LrG/+v1kM4a3rLldZCa/2h7gecMPs5o1RNSndD6FhvGc/hPoEhsPYt6HoAMy9u8ZS2SEBfrySlkJuYQl/+CTzpG13yo1UVlqDBQ7vhHHvnDwZgDW6qP94+OkbKNS9MZQ6HS0rIQC0GwCXPAub58PyN2uc6p8QxQOXdGf+2r3MWbXbpgCVy354xfrveMlz0Oms+ssnp4GphMyPmj42peqRmZlJSkpKjceQIUPsDuuUPH/YaV2G3gXbvoMvH4eOQ6Ft/+pTd5zXhe+25PL0vPUMSoyhY2yIjYGqk9q2CL55zpp8NvRO165p1Q3ap1qT1M6+p0nDU83LGONxk0v79etHRkZGs77mmbZ8tLwaAoAIjP4/CI6xJjGVHq0+5esjvHxtMiJw/4zVlFfoUFS3U7AbZt0Gsd2syWcN+SBIToP962Dv2qaLTzWroKAg8vLytJm3HsYY8vLyCAoKOu17tMwaAkBoKxjzJkwbCQt+D6OnVJ9KiA7h+dF9uW96BlO+/Yn7Lu5mY6CqhvJS+OhmKC+G8e+dmHzmqr5j4IvHrFqCU81Qea6EhARycnLIzc21OxS3FxQUREJCQv0FT6LlJgSApGEw7CFY/BdreYP+46pPjUppz6LNubz2zVbO7daKgZ2iT3ob1YwW/gFyVsC4aTUnn7kqJAZ6XA6ZM61Z7L4t+5+4N/D39ycpKcnuMLxCy2wycnbeo9BhKHz2ABzaVuPUs6P60DYyiAdmZFBUUm5TgKra2pmw4s26J581RPIEOJprjThSSrms5ScEXz8Y829rWOKsX1lNEg4RQf68Mj6FnPxjPDNvvY1BKvavh3n3Qsez4eJnzuxeXS+x+o/WfFh/WaVUtZafEMDai3nUFNizGv5bc6ZramIMd1/QlVkrc5i/dq9NAXq54gKYcQMERcC4qeB7hpMG/QKg31jYNN+a2KaUcol3JASAXlfBoF/D0n/8bMvFey7qRnKHKB6bs5Y9h4/bFKCXMsaafJa/wzH5zPWVIk8pOQ0qSmDDJ41zP6W8gPckBIBLn4f4PvDxHTVms/r7+vDq+BTKKw2/m7mGykod3tZsfngFNn0Glz4Hnc5uvPu2+wW06mGNNlJKucS7EoJ/sNUkUXbMWj/faansxFahPHNVH5Zuy+PN77ed4iaq0Wz7Dv47CfpcbU0mbEwiVi1h59KfDSZQStXNuxICWIufDX8Jti+GJZNrnBqXmsDwvm3465ebWbe78XcjUk4Kdlud/LFdGz75zFX9rwXEGr2klKqXSwlBRC4Xkc0ikiUij9ZxfpiIrBKRchEZ63T8AhHJcHoUi8hox7l3RGS707mUxntb9Rhwo7Ukwrd/hJ3LnN8Hf7qmH7Ghgdw7fTXHSytOcRN12n42+Sy8aV4nMsGai7LmwxoLHSql6lZvQhARX2AKMBzoDUwQkd61iu0EbgFqbFlljPnWGJNijEkBLgSOAV86FXm46rwxpvkW/RCBq16xPjBm/xqO51efigoJ4G/XJrMt9ygvLNjQbCF5lS8ftyafjfqHVWNrSskTID8bdi2rt6hS3s6VGsJgIMsYs80YUwpMB0Y5FzDGZBtj1gKnWhhoLPC5McY9trQKirS23izcY41/d/oGeU7XVkwc1pn3ftzJ1xv22xhkC7R2Jix/A4b+1uo7aGq9rgL/UMjQ7TWVqo8rCaE9sMvp9xzHsYZKA2rPFHpBRNaKyGQRCazrIhGZKCLpIpLe6GuZJAyEi56CjfNg5dQap353aXd6t43gkdlrOVDYsH1S1Uns32DtfNbxLGuJ8uYQGAa9R8L6T6BMhxQrdSquJIS6evsa1CArIm2BfsBCp8OPAT2BQUAM8Ehd1xpj3jDGpBpjUuPi4hrysq456x7ocpG1INr+E01EgX6+vJqWQlFJOQ9/tFZXWjxTVZPPAsOt+QZnOvmsIZLToKQANn/efK+plAdyJSHkAB2cfk8A9jTwda4FPjbGlFUdMMbsNZYSYCpW01Tz8/GBq/8JgREw69Yam7R3ax3O4yN68d2WXN5d2vANtJVD9eSz7MadfOaqxF9CRHudk6BUPVxJCCuAbiKSJCIBWE0/8xr4OhOo1VzkqDUg1q4Xo4F1Dbxn4wmLh2v+Bbmb4Iuag6huHNqJC3rE8cKCjWzZX2hTgB7uh1etyWeXTGrcyWeu8vG1hqBmfW1tr6qUqlO9CcEYUw7cjdXcsxGYaYxZLyKTRGQkgIgMEpEcYBzwLxGpXilORBKxahjf1br1+yKSCWQCrYDnz/ztnIEuF8K5D8CqabBuTvVhEeHPY5OJCPLj3g9XU1KuQ1EbZPtia/2o3qPgrN/aF0f/NDAVkDnLvhiUcnPiSW3jqampJj09veleoKIMpg6H3M1wx/cQnVh96ttNB7j1nRX8+twknriy9qhbVacje+BfwyA4Gm7/punmG7jqjfOhshzuWGJvHEo1MxFZaYxJra+c981UPhVff2upbLC2cKyo7vLggp7x3HRWJ/69ZDvfb9Wdm+pVXgozb7b6ZJpy8llDJE+AfZmwz77WSaXcmSaE2qITYeRrsDsdvn2hxqk/XNGLrvFh/G7mGvKPltZ9vbJ8+QTkLG+eyWeu6jsWfPxgrXYuK1UXTQh16XM1/OJma60jp123gvytoaj5x0p5dI4ORT2pzFmw/F/WgnV9r7E7mhNCY6HbZdbkuArdIU+p2jQhnMzlL0JcT5jzmxojU/q0i+Thy3qwcP1+ZqbvOsUNvNSBjTDvHsfks0l2R/NzyWlQtB+2L7I7EqXcjiaEkwkIsZa2KDkCH/8GKk+syvHrcztzdpdYnpm3gW25RTYG6WaKj1iTzwLCmn/ymau6XwZBUTonQak6aEI4lda94fI/Wc1GS/9efdjHR3j52hQC/Hx4YEYGZRWnWsLJSxgDc++CQ9vtmXzmKr9A6DsGNn5mJTClVDVNCPUZeCv0Gmlt5JJzYshrm8ggXrymH2tyCnj16602Bugm/vd32PiptUZR4jl2R3NqyROg/DhsmGt3JEq5FU0I9RGxRh2Ft7U2dCk+sXHO8H5tuTY1gSmLsli+/ZCNQdosewl8/Yxj8tnddkdTv4RUa2MebTZSqgZNCK4IjoYxb0FBDnx6f42lsp++qg8dY0J4YEYGBcfLTnGTFurIXvjoVojpDKOmNM3OZ42tanvNHUsgX9eoUqqKJgRXdRwCF/wB1s+B1f+pPhwa6Mcr41PYd6SYp+Z62YSnijL46BYoPeo+k89c1X+89VO311SqmiaEhjj3AUg6Dxb83lrewmFAx2juu6gbczP28Mnq3TYG2My+fBJ2/Qij/g7xPe2OpmGiOlqroOr2mkpV04TQED6+cM0bEBBqNZM4bbhy1/ldSO0UzZOfrGPXIffYFK5JZc6CZa/DkDutUTueKDkNDv1UY7CAUt5ME0JDhbex9k84sN5ansHBz9eHyeNTAHhwZgYVlS34W+eBTda2ox2GwqXP2R3N6es1EvyCYY1ur6kUaEI4Pd0usUbTrPg3bDixNUSHmBAmje7Diux8Xl+UZWOATah68lmo+04+c1VQhLXn8rrZUF5idzRK2U4Twum66GloNwDm3Q2Hd1YfHp3SnpHJ7Zj89VYydh22McAmYAzM/S0c2gbjpkJEW7sjOnPJadZQ4i1f2B2JUrbThHC6/AJg7NvWkhazb69eLE1EeG50X9pEBHH/9NUcLWlBi6gt/QdsnAcXPwOJ59odTePofD6EtdE5CUqhCeHMxHSGKydbI22+e7H6cGSwPy9fm8yOQ8eY9OkGGwNsRNk/wFdPW+3uZ99jdzSNp2p7za1fwtGDdkejlK1cSggicrmIbBaRLBF5tI7zw0RklYiUi8jYWucqRCTD8ZjndDxJRJaJyFYRmeHYr9nz9B8HKTfA4r/CthO7hA7pHMud53VhRvouvli318YAG8GRvdZ8A0+afNYQyROsndTWzbY7EqVsVW9CEBFfYAowHOgNTBCR2ntI7gRuAeoarnHcGJPieIx0Ov4SMNkY0w3IB247jfjdwxV/tpZCmDOxxrfM+y/uTv+ESB6dk8m+gmIbAzwD1ZPPimD8f6yO2JamdW9o0x8ydLSR8m6u1BAGA1nGmG3GmFJgOjDKuYAxJtsYsxZwadlPERHgQqBqx/NpwGiXo3Y3AaFWf8LxfPjkzuqJTgF+PrwyPoWSskp+91EGlZ44FPWrp6wmsZF/h/hedkfTdFKug70Z1n4OSnkpVxJCe8B5J5gcxzFXBYlIuoj8KCJVH/qxwGFjTFWPa0Pv6X7a9odLn7faon/8v+rDnePCeOqq3vyQlcfbP2y3McDTsG6O9V6G3AH9xtZf3pP1HQviq53Lyqu5khDqajBuyFfdjsaYVOA64BUR6dKQe4rIREdCSc/NdfPN7QffDj1GWJ2ve1ZXH04b1IFLe7fmz19sZsMeD1mD/8AmmHs3dBgCl3jw5DNXhcVZ80vWzoTKCrujUcoWriSEHKCD0+8JwB5XX8AYs8fxcxuwCBgAHASiRMSvvnsaY94wxqQaY1Lj4uJcfVl7iFibyofFW0tllxQ6DgsvjulPVIg/901fTXGZm3/glBTCzButXePGvWMNsfUGyWlQuAe2L7Y7EqVs4UpCWAF0c4wKCgDSgHn1XAOAiESLSKDjeSvgHGCDsXan/xaoaoe4GWgZu5WExMCYf0N+Nnz2YHV/QkxoAH8dl8zWA0X8aYEbt1NXTT7Ly7L6RSLa2R1R8+k+HAIjtdlIea16E4Kjnf9uYCGwEZhpjFkvIpNEZCSAiAwSkRxgHPAvEVnvuLwXkC4ia7ASwIvGmKqB+Y8AD4pIFlafwluN+cZs1elsOO9RyJxprabpMKx7HL86J4lpS3fw7aYDNgZ4CkunWDuJXfQ0JA2zO5rm5R8Efa+2Jt85andKeRMxHrT0b2pqqklP95CVKSsrYNpIqy/hN99Bq24AFJdVMHrKDxwsKuGL+4fRKizQ5kCdZP8A066CHsOt/Q1a2nwDV+xcBm9fCqNft0YeKdUCiMhKR1/uKelM5aZStVS2XyDMurV68bQgf19eTRvAkeJyfj9rLW6TkAv3WXFGJ8Lo//POZADQYTBEJ9Wo2SnlLTQhNKXI9taH675Mazy/Q4824Tw2vCffbDrAe8t2nuIGzaRq8llJoVUzCIq0OyL7iFgzl7d/D4d31V9eqRZEE0JT6zHc2kRm2T9h04Lqw7ecnch53eN4/rMNZB2wub36q6dh51K46jVr1q63638tYKw+IKW8iCaE5nDJs9bSCHPvggJri00R4S/j+hMa6Me9H2ZQUm7TUNR1c+DHKTB4orUuk4KYJOh4tjXayF2a9JRqBpoQmoNfIIydCuWlMOf26olP8eFBvDSmPxv2HuHlL7c0f1y5m63JZwmD4dIXmv/13VlyGhzcArtX2R2JUs1GE0JzadUVRvwNdvwAi/9SffiS3q25bkhH3vh+G//Lasbll0sKrZ3P/IO9a/KZq/qMBr8g7VxWXkUTQnNKmQD9x8N3L1lDPB2eGNGLpFahPDhzDYePlTZ9HMZYNYOqyWeRnr2MVJMIioSeI2DdLKtmp5QX0ITQ3Eb8zRraOfvXcOwQACEBfryWNoC8oyX84ePMph+K+uP/wYZP4KKnoPN5Tftanix5grWC7dYv7Y5EqWahCaG5BYZb38qP5lpLRDg+/Pu2j+TBS3qwIHMfs1bmNN3r7/gffPkk9LwSzrm/6V6nJeh8AYTGa7OR8hqaEOzQbgBcMgk2L4Dlb1QfnjisM0M7x/DMvPXsyDva+K9buM+ab+Dtk89c5etnDUHdsrC6NqdUS6YJwS5D74Rul8GXT8DetQD4+ggvX5uCr49w3/QMyipc2m/INRVl8NGtUHzEsfOZF08+a4jkNKgs0+01lVfQhGAXEetbekisY6nsIgDaRQXzx2v6kbHrMH//JqvxXu/rZ2Dn/2Dka9C6T+Pdt6Vr0w9a99VmI+UVNCHYKbSVtd5RXhZ8/vvqw1f2b8c1v2jPP77ZysodjdBUsf5jWPoPGHS7YxauapDkCbB7JeTaMFdEqWakCcFuScNg2MOQ8b61W5fDsyP70D46mPumZ1BYXHb696+efDYILvtjIwTshfqNA/GBtbpPgmrZNCG4g/MegY5nwWcPQN5PAIQH+fPK+AHsLSjm6bnr67nBSZQUwYwbrQlW46bp5LPTFd4aulwEa2ZAZSP26yjlZjQhuANfP7jmTfDxs/oTHBOhBnaK5u4LujJn9W7mrXF511KLMTDvbsjbqpPPGkNyGhzJgR1L7I5EqSajCcFdRHWw9mPemwH/fbb68D0XdmVAxyge/ziT3YePu36/H1+3+g4ufFInnzWGniMgMEK311QtmiYEd9LrKhj0a6sDeIs1O9bP14dXxw+gstLw4IwMKipdmMW8Yyl89ST0GAHnPtDEQXsJ/2DoPcraXrS0CeaIKOUGXEoIInK5iGwWkSwRebSO88NEZJWIlIvIWKfjKSKyVETWi8haERnvdO4dEdkuIhmOR0rjvCUPd+kL1jDHT+6AI3sB6BgbwrOj+rJs+yH+tfinU19fuN+afBbVEa5+XSefNaaU66C0CDZ+ZnckSjWJehOCiPgCU4DhQG9ggojU3kVlJ3AL8EGt48eAm4wxfYDLgVdEJMrp/MPGmBTHI+M030PL4h9ktfmXHYePJ1YvlT3mF+0Z0a8tL3+5hcycgrqvrSiztsEsLoBrdfJZo+swFKI66ZwE1WK5UkMYDGQZY7YZY0qB6cAo5wLGmGxjzFqgstbxLcaYrY7ne4ADQFyjRN6SxfWA4X+G7YthyWTA2lDnhav7EhceyH3TV3OstPzn1339jLW89lWvQpu+zRuzN/DxsTqXty2CIw3s5FfKA7iSENoDzpvL5jiONYiIDAYCAOc2jxccTUmTRSSwofds0QbcAH3HwLd/hJ3LAIgKCeBv1yazPe8oQ174L7dOXc7ri35i1c58yjOrJp/9GpLH13Nzddr6jwdMjTkjSrUUriSEuhqhG7Q+s4i0Bf4D3GqMqapFPAb0BAYBMcAjJ7l2ooiki0h6bm5uQ17Ws4nAlZMhMgFm32Ytwwyc3aUV024dzFUp7diVf5yXvtjEw69/RMmsO8gK6Mk//H/F0p/yKC6zaUvOli62C3QYYjUb6faaqoXxc6FMDtDB6fcEwOX6sohEAPOBJ4wxP1YdN8bsdTwtEZGpwEN1XW+MeQN4AyA1NdW7/g8MirS23nz7Uph3j9UvIMKw7nEM6261vB08lEfQ1EvgeBCTgh/h+2+zMd/QftUUAAAe30lEQVRkE+DrQ3KHSAYnxTA4KZaBnaIJC3TlP7eqV3KaNYlwb4a1cq1SLYQrnxArgG4ikgTsBtKA61y5uYgEAB8D7xpjPqp1rq0xZq+ICDAaWNegyL1FwkBrI5uvnoL0t2HQbSfOGUOr/z4ERdvhxo95t/P5FBwvY+WOQyzbdohl2w/xz++2MeXbn/D1Efq0i2CII0EMSowmKkRnLp+WPlfD549acxI0IagWRFzZnUtErgBeAXyBt40xL4jIJCDdGDNPRAZhffBHA8XAPmNMHxG5AZgKOK+9cIsxJkNEvsHqYBYgA7jDGFN0qjhSU1NNenp6w9+lp6ushPfHQvYSmPjtidVKf3wdvnjUShi//F2dlx4tKWf1zsMs257Hsu2HyNh1mNJyq9WuZ5twRw3CesSHBzXXO/J8M2+G7O/hd5vB19/uaJQ6JRFZaYxJrbdck2/X2Ii8NiEAFB2A18+B4GgrKezLhHdGQLdLYfz71ggYFxSXVbA2p4DljgSxckc+x0qt/obOrUJrJIiE6JCmfEeebfMX8OF4mDAdegy3OxqlTkkTQkv007fwn6utJosd/7Nmz05cBMFR9V15UmUVlazfc4Tl2/NYvv0Qy7cf4kixNaS1fVSwo4nJeiS1CkV0opulogz+1hMSz4Fr37U7GqVOydWEoL2MnqTLBXDu/dbcBL9guGH2GSUDAH9fH1I6RJHSIYqJw7pQWWnYtK/QShDZh1i8NZc5q3cD0CoskCFJMQzpbCWI7vHh+Ph4aYLw9beWxU5/yxoBFhxtd0RKnTFNCJ7mgsetmchdL26SyWc+PkLvdhH0bhfBLeckYYxh28GjLNt2qLqZaX6mNUAsMtifQYkx1Umid9sI/Hy9aHms5DRY9jqsm1Ozs18pD6VNRqpBjDHk5B+vbl5atj2P7LxjAIQG+DLQkSAGJ8XQPyGSQD9fmyNuQsbA62dDQBj8+iu7o1HqpLTJSDUJEaFDTAgdYkIYMzABgP1HiqsTxPLth/jLws0ABPr5MKBjFIOTYhmSFMOAjlGEBLSgf3IiVi3hq6esjY1iu9gdkVJnRGsIqtEdOlrKiuwTCWL9ngIqDfj5CP0SIhniSBADE6OJCPLwIZtH9sLk3vDLh+DCx+2ORqk66Sgj5TaOFJexckd+dYJYm3OYsgqDj0CvthEMTophiGOyXGyYBy5p9Z+rIS8L7l3j8vBfpZqTJgTlto6XVrB6V76jo/oQq3bmU+KYLNctPqx6mOuQpFjaRHrAZLm1M2HO7XDLAmsYqlJuRvsQlNsKDvDl7C6tOLtLKwBKyyvJ3H2YZY4axNyMPby/bCcAnWJDGJx4IkF0iAl2v7kQPUdYHctrPtCEoDya1hCU2ymvqGTj3kKWVU2Wyz7E4WNlALSJCLKSQ2drNFOXuDD3SBCf/NbaXvOhLRCgM7yVe9EmI9ViVFYath4oqp4HsWz7IXILSwCIDQ2gd7sIusWH0711GN1ah9E1PpzI4GburN7+PUy7Esa8Bf3G1l9eqWakCUG1WMYYduQdc9Qg8tm8/whZB4ooLjuxYV/riEC6xYfTrXVY9c/u8eFEhjRRoqishFf7W7vd3TC7aV5DqdOkfQiqxRIREluFktgqlPGDOgJWLSIn/zhbDxSy9UARW/YXknWgiOnLd3HcabOguPBAusWH0b11OF3jw6qfR4ee4VLgPj7WbmpLXobCfRDe5szup5QNNCGoFsHHR+gYG0LH2BAu6tW6+nhlpWH34eNkOZLE1gNFbD1QxEfpuzhaeiJRtAoLcKpRhNGtdTjd4sMaNgw2OQ2+/ytkfgRn39OYb0+pZqFNRsorGWPYU1DM1v2FbN1fVF2z2Lq/iKKS8upyMaEBjgQRVqMJqlVYQN2d2W9eBOXFcOcPzfhulDo1bTJS6hREhPZRwbSPCub8HvHVx40x7DtSzNb9J5qdth4oYm7GHgqLTySK6BB/usWH07V1GN2dahRxyWnIgodg71po29+Ot6bUadOEoJQTEaFtZDBtI4Or960GK1EcKCypThRbDxSRdaCQ+Wv38sHxsupyHYJi+AY/ls3+O1tS/mB1ZrcOJz480D2Gxyp1CpoQlHKBiNA6IojWEUGc261V9XFjDLlFVqLY6kgUqzYNodfBhdz82VVUYK32Gh7kV7Mzu7U1TLZNRJAmCuU2XEoIInI58CrWnsr/Nsa8WOv8MKw9l/sDacaYWU7nbgaecPz6vDFmmuP4QOAdIBhYANxnPKlDQymsRBEfHkR8eBDndHUkik2/henXseo6X9aHDrGanRw1i6827Gf6il3V14cF+tE1PsyaQ1HVBNU6nHaRmihU86s3IYiILzAFuATIAVaIyDxjzAanYjuBW4CHal0bAzwNpAIGWOm4Nh94HZgI/IiVEC4HPj/TN6SU7bpeAsExRG6exdnjRlQv0VElr6ikerRT1v5Ctuwv4ptNucxMz6kuExrgS1dHv4RzzaJ9VLD37lKnmpwrNYTBQJYxZhuAiEwHRgHVCcEYk+04V1nr2suAr4wxhxznvwIuF5FFQIQxZqnj+LvAaDQhqJbAL8CarbxyGhw//LNtTmPDAokNC2Ro59gax/OPljoSxYmRT4u35DJr5YlEERLgS9f4MMccivDqmkVCtCYKdeZcSQjtgV1Ov+cAQ1y8f13Xtnc8cuo4rlTLkJwGy9+ADZ/AwFtcuiQ6NKB6pVdnh4+VVo92qhr59L+sPOas2l1dJsjfh67xYXRuFUZSq9DqR2Kr0OZfxkN5LFcSQl1fO1xt6z/ZtS7fU0QmYjUt0bFjRxdfVimbtfsFtOoBa6a7nBBOJiokgNTEGFITayaKguNlZDlGO23ZbyWM1bvy+XTtHpx742JDA6qTQ41kERtKcEAL3uJUWdu8rnrX5eKuJIQcoIPT7wnAHhfvnwOcX+vaRY7jCa7c0xjzBvAGWBPTXHxdpexVtb3mf5+FQ9sgpnOjv0RksD8DO0UzsFN0jeMl5RXsOnSMbblH2X7wKNl5R9mWe5Tvt9ZsfgJoGxlUI0lUJY4O0SEE+OlmPx7NGPjyCVj6D5cvcSUhrAC6iUgSsBtIA65z8f4LgT+KSNW/2EuBx4wxh0SkUESGAsuAm4C/uxy1Up6g/7Xw30nWBjrnP9psLxvo50vX+HC6xof/7FxRSTnZjiSxPfco2/OspDE/c2/1EuMAvj5Ch+jg6lpFZ6caRrtI7a9we8bA54/A8n/BoNuBv7l0mUtLV4jIFVjDSn2Bt40xL4jIJCDdGDNPRAYBHwPRQDGwzxjTx3Htr4A/OG71gjFmquN4KieGnX4O3FPfsFNdukJ5nGkj4fAOuDfDqjW4sfyjpVaCyHXUKg4eJfuglTCOOa37FODnQ2JsSHVtorOj+SkpLpS4MJ2AZ7vKSljwO0h/G4b+Fi57AfHx0eWvlbJdxofwyR3wq4XQcajd0ZyWqlna2x3JwfmxM+8YpRUnBheGBfqR2CqEpFZhJMWGkBQX6nge2nRLj6sTKivg03th9Xtwzv1w8TMgomsZKeUWel0F838HGR94bEJwnqVde6hsRaVhz+HjNWoT2w4eZc2uw8xfu4dKp++bMVWd27GhdI5z1CpahZLYKoSQAP0oOmOVFfDJXbB2Ogz7PVzwhwbXSvW/glJNKTAMeo+E9Z/A8JfAP9juiBqVr4/QISaEDjEhnOe09hNUdW4fd9Qmith+8BjbDxaxJCuX2atqdm63iXB0bseFkhR7onO7Y4x2brukohw+ngjrZsMFT8B5D5/WbTQhKNXUktNgzYew+XPoe43d0TQbq3PbmkQHrWucO1pSTnbeUbIdSaKqhvF55l7ynTq3fQQ6xIRU1yacH+2igvHVzm2oKINZv4KN8+DiZ+Hc+0/7VpoQlGpqib+EiPbWnAQvSginEhroR592kfRpF/mzc4ePlVb3UWQ7mqC2HzxKevahGpsaBfj60Ck2pLpj27mTO85bVpctL4GPboHNC+CyP8FZd53R7TQhKNXUfHytIag/vAZFByAsvv5rvFhUSAADOgYwoGPN+RXGGHJP0rn93ebcGp3boQG+tIsKpk1kEG0jg2gTGUybiKrn1s/IYH/PThplxTDjBsj6Cq74Kwy+/YxvqQlBqebQPw2WTIbMWWf8Lc5biQjxEUHERwQx5CSd284T8fYVFLP3SDFb9udyoLCE2gMqg/x9aOtIFG2cEoWVOKxkEhsa4J5zLkqPwfTrYNsiuOrVM54NX0UTglLNIb4ntBsAaz7QhNAEnDu3hxH3s/NlFZXkFpawt6DYShQFx9l/pLj69+XbD7H/SDHllTWzhr+vNcKqKmlU1TaqahptIoKIDw/Ez7cZO75LiuDDNMheAqOmwIDrG+3WmhCUai7J18HnD8O+ddCmr93ReBV/Xx/aRQXTLurko7wqKw0Hj5awv6CEvQXH2eeUMPYWHGfd7gK+2rCfkvKaizr7CMSFB1qJokbiOFHbaB0ZSKBfI6wbVVII74+DXcvgmjespshGpAlBqebSdwwsfMwaJ97mebujUbX4+JzY7Khfws87u8Hqxyg4XuaUKIrZV3Dc+nmkmJ9yi/gh6yCFJeU/uzY2NKA6WbSOqLu2ERp4io/k4gJ4bwzsXgVj3mqSAQqaEJRqLqGx0O0ya22ji54BX/3fz9OICFEhAUSFBNCrbcRJyxUWl1U3Se0tKGa/oz9jX0Exuw8Xs3JHfo3htVUigvwc/Rk1axsJwSUMXHwbQXkbYNw7SO+RTfL+9F+kUs0pOQ02z4fti6DrxXZHo5pIeJA/4UH+dS4wWKW4rKJGP0bt2samvUfILSohyhzhvYA/4SO7ua3sfpZ+GEDbyEU1O8JrjaKKCTm9znBNCEo1p+6XQVCUNSdBE4JXC/L3pVNsKJ1iQ09apuzIfnh3FL75+1gx9HXODkqls1NtY9m2ujvDA3x9aB0ZSNsIa7SUqzQhKNWc/AKtvoSMD6D4CASdvNlBebnCffi/OxIO74TrZzKk8/l1blVZUWnIKyqprlnUrm2syTns8ktqQlCquaVcB+lvwYa58Isb7Y5GuaOC3TDtKijcBzfMgsRzT1rU1+fE/Izkk5SR37v2srpqlFLNrf1AiO1qNRspVdvhnfDOFdas9hvnnDIZNDZNCEo1t6rtNXcsgfwddkej3El+NkwdAcfy4aZPmn3JdE0IStmh/*******************************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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["losses[['loss','val_loss']].plot()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["['loss', 'accuracy']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["classifier.metrics_names"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.06797713786363602, 0.9777665734291077]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["classifier.evaluate_generator(test_set)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from tensorflow.keras.preprocessing import image"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Parasitised'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "test_image = image.load_img('/kaggle/input/files1/Malaria Cells/single_prediction/Parasitised.png', target_size = (64, 64))\n", "test_image = image.img_to_array(test_image)\n", "test_image = np.expand_dims(test_image, axis = 0)\n", "result = classifier.predict(test_image)\n", "training_set.class_indices\n", "if result[0][0] == 1:\n", "    prediction = 'Uninfected'\n", "else:\n", "    prediction = 'Parasitised'\n", "prediction"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# Importe\n", "from keras.models import Sequential\n", "from keras.layers import Convolution2D, MaxPooling2D, Flatten, Dense\n", "from keras.preprocessing.image import ImageDataGenerator\n", "from keras.callbacks import EarlyStopping\n", "from tensorflow.keras.preprocessing import image\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Initialisiere das CNN\n", "classifier = Sequential()\n", "\n", "# Step1 - Convolution\n", "classifier.add(Convolution2D(64, 3, 3, input_shape=(64, 64, 3), activation='relu'))\n", "\n", "# Step2 - Pooling\n", "classifier.add(MaxPooling2D(pool_size=(2, 2)))\n", "\n", "# Adding a second convolution layer\n", "classifier.add(Convolution2D(64, 3, 3, activation='relu'))\n", "classifier.add(MaxPooling2D(pool_size=(2, 2)))\n", "\n", "# Step3 - Flattening\n", "classifier.add(<PERSON>ten())\n", "\n", "# Step4 - Full Connection\n", "classifier.add(Dense(output_dim=128, activation='relu'))\n", "classifier.add(Dense(output_dim=1, activation='sigmoid'))\n", "\n", "# Compiling the CNN\n", "classifier.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "\n", "# Data Augmentation\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    shear_range=0.2,\n", "    zoom_range=0.2,\n", "    horizontal_flip=True\n", ")\n", "\n", "test_datagen = ImageDataGenerator(rescale=1./255)\n", "\n", "training_set = train_datagen.flow_from_directory(\n", "    'data/train',\n", "    target_size=(64, 64),\n", "    batch_size=32,\n", "    class_mode='binary'\n", ")\n", "\n", "test_set = test_datagen.flow_from_directory(\n", "    'data/test',\n", "    target_size=(64, 64),\n", "    batch_size=32,\n", "    class_mode='binary'\n", ")\n", "\n", "# Early Stopping\n", "early_stop = EarlyStopping(monitor='val_loss', patience=2)\n", "\n", "# Train the model\n", "history = classifier.fit(\n", "    training_set,\n", "    steps_per_epoch=training_set.samples // 32,\n", "    epochs=10,\n", "    validation_data=test_set,\n", "    validation_steps=test_set.samples // 32,\n", "    callbacks=[early_stop]\n", ")\n", "\n", "# Plot training history\n", "losses = pd.<PERSON><PERSON><PERSON><PERSON>(history.history)\n", "losses[['loss', 'val_loss']].plot()\n", "plt.show()\n", "\n", "# Evaluate the model\n", "classifier.evaluate(test_set)\n", "\n", "# Predict a single image\n", "test_image = image.load_img('data/single_prediction/Parasitised.png', target_size=(64, 64))\n", "test_image = image.img_to_array(test_image)\n", "test_image = np.expand_dims(test_image, axis=0)\n", "result = classifier.predict(test_image)\n", "\n", "if result[0][0] == 1:\n", "    prediction = 'Uninfected'\n", "else:\n", "    prediction = 'Parasitised'\n", "\n", "print(prediction)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}}, "nbformat": 4, "nbformat_minor": 4}