# Malaria Detection System

Ein KI-gestütztes System zur Erkennung von Malaria-Parasiten in Blutzellen-Bildern mit Deep Learning.

## Features

- **Web Interface**: Benutzerfreundliche Weboberfläche zum Hochladen und Analy<PERSON><PERSON> von Bildern
- **Deep Learning**: CNN-Modell mit Transfer Learning (VGG16) für hohe Genauigkeit
- **Drag & Drop**: Einfaches Hochladen von Bildern per Drag & Drop
- **Echtzeit-Vorhersagen**: Schnelle Analyse und Ergebnisanzeige
- **Konfidenz-Bewertung**: Vertrauenswerte für jede Vorhersage
- **API-Endpunkt**: RESTful API für programmatischen Zugriff

## Projektstruktur

```
Malaria/
├── app.py                      # Flask Web-Anwendung
├── requirements.txt            # Python-Abhängigkeiten
├── README.md                   # Diese Datei
├── src/
│   ├── Malaria.py             # Haupttraining-Skript
│   └── prediction_service.py   # Vorhersage-Service
├── templates/                  # HTML-Templates
│   ├── base.html
│   ├── index.html
│   ├── result.html
│   ├── 404.html
│   └── 500.html
├── static/
│   └── uploads/               # Hochgeladene Bilder
├── data/                      # Trainingsdaten
│   ├── train/
│   │   ├── Parasitized/
│   │   └── Uninfected/
│   └── test/
│       ├── Parasitized/
│       └── Uninfected/
├── models/                    # Gespeicherte Modelle
└── logs/                     # Training-Logs
```

## Installation

### 1. Repository klonen oder herunterladen

```bash
git clone <repository-url>
cd Malaria
```

### 2. Virtuelle Umgebung erstellen (empfohlen)

```bash
python -m venv malaria_env
# Windows:
malaria_env\Scripts\activate
# Linux/Mac:
source malaria_env/bin/activate
```

### 3. Abhängigkeiten installieren

```bash
pip install -r requirements.txt
```

## Verwendung

### 1. Modell trainieren

Bevor Sie die Web-Anwendung verwenden können, müssen Sie ein Modell trainieren:

```bash
python src/Malaria.py
```

Das Training kann je nach Hardware 30-60 Minuten dauern. Das trainierte Modell wird im `models/` Verzeichnis gespeichert.

### 2. Web-Anwendung starten

```bash
python app.py
```

Die Anwendung ist dann unter `http://localhost:5000` verfügbar.

### 3. Bilder analysieren

1. Öffnen Sie `http://localhost:5000` in Ihrem Browser
2. Laden Sie ein Blutzellen-Bild hoch (PNG, JPG, etc.)
3. Klicken Sie auf "Analyse starten"
4. Sehen Sie sich das Ergebnis mit Konfidenz-Bewertung an

## API-Verwendung

### Vorhersage-Endpunkt

```bash
curl -X POST -F "file=@path/to/image.jpg" http://localhost:5000/api/predict
```

Antwort:
```json
{
  "prediction": "Uninfected",
  "confidence": 0.95,
  "probability": 0.95,
  "timestamp": "2024-01-01T12:00:00"
}
```

### Modell-Info

```bash
curl http://localhost:5000/model-info
```

### Health Check

```bash
curl http://localhost:5000/health
```

## Datenformat

Das System erwartet Bilder von Blutzellen, idealerweise:
- **Format**: PNG, JPG, JPEG, GIF, BMP, TIFF
- **Größe**: Wird automatisch auf 128x128 Pixel skaliert
- **Qualität**: Mikroskopische Aufnahmen für beste Ergebnisse

## Modell-Details

- **Architektur**: CNN mit VGG16 Transfer Learning
- **Input-Größe**: 128x128x3 (RGB)
- **Output**: Binäre Klassifikation (Parasitized/Uninfected)
- **Training**: Data Augmentation, Early Stopping, Learning Rate Scheduling

## Konfiguration

Wichtige Parameter in `src/Malaria.py`:
- `IMAGE_SIZE`: Bildgröße (Standard: 128)
- `BATCH_SIZE`: Batch-Größe (Standard: 32)
- `EPOCHS`: Maximale Epochen (Standard: 30)
- `LEARNING_RATE`: Lernrate (Standard: 0.001)

## Troubleshooting

### Häufige Probleme

1. **"No model loaded"**: Trainieren Sie zuerst ein Modell mit `python src/Malaria.py`
2. **Speicher-Fehler**: Reduzieren Sie `BATCH_SIZE` in der Konfiguration
3. **Langsames Training**: Verwenden Sie eine GPU für bessere Performance

### GPU-Unterstützung

Für GPU-Training installieren Sie tensorflow-gpu:
```bash
pip install tensorflow-gpu==2.13.0
```

## Medizinischer Haftungsausschluss

⚠️ **WICHTIG**: Dieses System dient nur zu Demonstrationszwecken und ersetzt keine professionelle medizinische Diagnose. Bei Verdacht auf Malaria konsultieren Sie einen Arzt oder ein medizinisches Labor.

## Lizenz

Dieses Projekt ist für Bildungszwecke entwickelt worden.

## Beitragen

1. Fork das Repository
2. Erstellen Sie einen Feature-Branch
3. Committen Sie Ihre Änderungen
4. Pushen Sie zum Branch
5. Erstellen Sie einen Pull Request

## Support

Bei Fragen oder Problemen erstellen Sie bitte ein Issue im Repository.
